import "./app.css";
import App from "./App.svelte";

import * as Sen<PERSON> from "@sentry/svelte";

// Initialize the Sentry SDK here
Sentry.init({
  dsn: "http://12345@localhost/123",
  debug: true,

  integrations: [
    Sentry.browserTracingIntegration(),
  ],

  // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
  tracesSampleRate: 1.0,

  // Set tracePropagationTargets to control for which URLs trace propagation should be enabled
  // This ensures sentry-trace and baggage headers are attached to requests to the Rails backend
  tracePropagationTargets: ["localhost", /^https?:\/\/localhost:4000/],

  environment: "test",
});

// Add some debug logging
console.log('[Sentry] Initialized with debug=true and tracePropagationTargets for localhost:4000');

const app = new App({
  target: document.getElementById("app"),
});

export default app;
