var ao=Object.defineProperty;var co=(t,e,n)=>e in t?ao(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var ln=(t,e,n)=>co(t,typeof e!="symbol"?e+"":e,n);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function ae(){}function Ns(t){return t()}function Ir(){return Object.create(null)}function en(t){t.forEach(Ns)}function Cs(t){return typeof t=="function"}function uo(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function fo(t){return Object.keys(t).length===0}function z(t,e){t.appendChild(e)}function Ps(t,e,n){t.insertBefore(e,n||null)}function Kn(t){t.parentNode&&t.parentNode.removeChild(t)}function Et(t){return document.createElement(t)}function Zn(t){return document.createTextNode(t)}function $e(){return Zn(" ")}function lo(t,e,n,r){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n,r)}function re(t,e,n){n==null?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}function po(t){return Array.from(t.childNodes)}function Os(t,e){e=""+e,t.data!==e&&(t.data=e)}let Qn;function se(t){Qn=t}const Mt=[],Rr=[];let Ht=[];const wr=[],mo=Promise.resolve();let Nn=!1;function ho(){Nn||(Nn=!0,mo.then(Ls))}function Cn(t){Ht.push(t)}const pn=new Set;let $t=0;function Ls(){if($t!==0)return;const t=Qn;do{try{for(;$t<Mt.length;){const e=Mt[$t];$t++,se(e),go(e.$$)}}catch(e){throw Mt.length=0,$t=0,e}for(se(null),Mt.length=0,$t=0;Rr.length;)Rr.pop()();for(let e=0;e<Ht.length;e+=1){const n=Ht[e];pn.has(n)||(pn.add(n),n())}Ht.length=0}while(Mt.length);for(;wr.length;)wr.pop()();Nn=!1,pn.clear(),se(t)}function go(t){if(t.fragment!==null){t.update(),en(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Cn)}}function _o(t){const e=[],n=[];Ht.forEach(r=>t.indexOf(r)===-1?e.push(r):n.push(r)),n.forEach(r=>r()),Ht=e}const So=new Set;function yo(t,e){t&&t.i&&(So.delete(t),t.i(e))}function Eo(t,e,n){const{fragment:r,after_update:s}=t.$$;r&&r.m(e,n),Cn(()=>{const i=t.$$.on_mount.map(Ns).filter(Cs);t.$$.on_destroy?t.$$.on_destroy.push(...i):en(i),t.$$.on_mount=[]}),s.forEach(Cn)}function bo(t,e){const n=t.$$;n.fragment!==null&&(_o(n.after_update),en(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function To(t,e){t.$$.dirty[0]===-1&&(Mt.push(t),ho(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function vo(t,e,n,r,s,i,o=null,c=[-1]){const a=Qn;se(t);const u=t.$$={fragment:null,ctx:[],props:i,update:ae,not_equal:s,bound:Ir(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(a?a.$$.context:[])),callbacks:Ir(),dirty:c,skip_bound:!1,root:e.target||a.$$.root};o&&o(u.root);let d=!1;if(u.ctx=n?n(t,e.props||{},(f,l,...p)=>{const h=p.length?p[0]:l;return u.ctx&&s(u.ctx[f],u.ctx[f]=h)&&(!u.skip_bound&&u.bound[f]&&u.bound[f](h),d&&To(t,f)),l}):[],u.update(),d=!0,en(u.before_update),u.fragment=r?r(u.ctx):!1,e.target){if(e.hydrate){const f=po(e.target);u.fragment&&u.fragment.l(f),f.forEach(Kn)}else u.fragment&&u.fragment.c();e.intro&&yo(t.$$.fragment),Eo(t,e.target,e.anchor),Ls()}se(a)}class Io{constructor(){ln(this,"$$");ln(this,"$$set")}$destroy(){bo(this,1),this.$destroy=ae}$on(e,n){if(!Cs(n))return ae;const r=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return r.push(n),()=>{const s=r.indexOf(n);s!==-1&&r.splice(s,1)}}$set(e){this.$$set&&!fo(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const Ro="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(Ro);const g=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,T=globalThis,bt="10.5.0";function kt(){return nn(T),T}function nn(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||bt,e[bt]=e[bt]||{}}function pe(t,e,n=T){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[bt]=r[bt]||{};return s[t]||(s[t]=e())}const wo=["debug","info","warn","error","log","assert","trace"],Ao="Sentry Logger ",Ge={};function zt(t){if(!("console"in T))return t();const e=T.console,n={},r=Object.keys(Ge);r.forEach(s=>{const i=Ge[s];n[s]=e[s],e[s]=i});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}function ko(){er().enabled=!0}function No(){er().enabled=!1}function xs(){return er().enabled}function Co(...t){tr("log",...t)}function Po(...t){tr("warn",...t)}function Oo(...t){tr("error",...t)}function tr(t,...e){g&&xs()&&zt(()=>{T.console[t](`${Ao}[${t}]:`,...e)})}function er(){return g?pe("loggerSettings",()=>({enabled:!1})):{enabled:!1}}const m={enable:ko,disable:No,isEnabled:xs,log:Co,warn:Po,error:Oo},$s=50,Rt="?",Ar=/\(error: (.*)\)/,kr=/captureMessage|captureException/;function Ms(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const i=[],o=n.split(`
`);for(let c=r;c<o.length;c++){let a=o[c];a.length>1024&&(a=a.slice(0,1024));const u=Ar.test(a)?a.replace(Ar,"$1"):a;if(!u.match(/\S*Error: /)){for(const d of e){const f=d(u);if(f){i.push(f);break}}if(i.length>=$s+s)break}}return xo(i.slice(s))}}function Lo(t){return Array.isArray(t)?Ms(...t):t}function xo(t){if(!t.length)return[];const e=Array.from(t);return/sentryWrapped/.test(we(e).function||"")&&e.pop(),e.reverse(),kr.test(we(e).function||"")&&(e.pop(),kr.test(we(e).function||"")&&e.pop()),e.slice(0,$s).map(n=>({...n,filename:n.filename||we(e).filename,function:n.function||Rt}))}function we(t){return t[t.length-1]||{}}const mn="<anonymous>";function tt(t){try{return!t||typeof t!="function"?mn:t.name||mn}catch{return mn}}function Nr(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Me={},Cr={};function gt(t,e){Me[t]=Me[t]||[],Me[t].push(e)}function _t(t,e){if(!Cr[t]){Cr[t]=!0;try{e()}catch(n){g&&m.error(`Error while instrumenting ${t}`,n)}}}function q(t,e){const n=t&&Me[t];if(n)for(const r of n)try{r(e)}catch(s){g&&m.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${tt(r)}
Error:`,s)}}let hn=null;function Ds(t){const e="error";gt(e,t),_t(e,$o)}function $o(){hn=T.onerror,T.onerror=function(t,e,n,r,s){return q("error",{column:r,error:s,line:n,msg:t,url:e}),hn?hn.apply(this,arguments):!1},T.onerror.__SENTRY_INSTRUMENTED__=!0}let gn=null;function Fs(t){const e="unhandledrejection";gt(e,t),_t(e,Mo)}function Mo(){gn=T.onunhandledrejection,T.onunhandledrejection=function(t){return q("unhandledrejection",t),gn?gn.apply(this,arguments):!0},T.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}const Hs=Object.prototype.toString;function nr(t){switch(Hs.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return et(t,Error)}}function Vt(t,e){return Hs.call(t)===`[object ${e}]`}function Bs(t){return Vt(t,"ErrorEvent")}function Pr(t){return Vt(t,"DOMError")}function Do(t){return Vt(t,"DOMException")}function Q(t){return Vt(t,"String")}function rr(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function ce(t){return t===null||rr(t)||typeof t!="object"&&typeof t!="function"}function ue(t){return Vt(t,"Object")}function rn(t){return typeof Event<"u"&&et(t,Event)}function Fo(t){return typeof Element<"u"&&et(t,Element)}function Ho(t){return Vt(t,"RegExp")}function me(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function Bo(t){return ue(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function et(t,e){try{return t instanceof e}catch{return!1}}function Us(t){return!!(typeof t=="object"&&t!==null&&(t.__isVue||t._isVue))}function js(t){return typeof Request<"u"&&et(t,Request)}const sr=T,Uo=80;function K(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let i=0,o=0;const c=" > ",a=c.length;let u;const d=Array.isArray(e)?e:e.keyAttrs,f=!Array.isArray(e)&&e.maxStringLength||Uo;for(;n&&i++<r&&(u=jo(n,d),!(u==="html"||i>1&&o+s.length*a+u.length>=f));)s.push(u),o+=u.length,n=n.parentNode;return s.reverse().join(c)}catch{return"<unknown>"}}function jo(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(sr.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e!=null&&e.length?e.filter(o=>n.getAttribute(o)).map(o=>[o,n.getAttribute(o)]):null;if(s!=null&&s.length)s.forEach(o=>{r.push(`[${o[0]}="${o[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const o=n.className;if(o&&Q(o)){const c=o.split(/\s+/);for(const a of c)r.push(`.${a}`)}}const i=["aria-label","type","name","title","alt"];for(const o of i){const c=n.getAttribute(o);c&&r.push(`[${o}="${c}"]`)}return r.join("")}function he(){try{return sr.document.location.href}catch{return""}}function qs(t){if(!sr.HTMLElement)return null;let e=t;const n=5;for(let r=0;r<n;r++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}function We(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function Or(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{Us(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function De(t,e,n=!1){return Q(t)?Ho(e)?e.test(t):Q(e)?n?t===e:t.includes(e):!1:!1}function ut(t,e=[],n=!1){return e.some(r=>De(t,r,n))}function H(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&Gs(s,r);try{t[e]=s}catch{g&&m.log(`Failed to replace method "${e}" in object`,t)}}function B(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{g&&m.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function Gs(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,B(t,"__sentry_original__",e)}catch{}}function ir(t){return t.__sentry_original__}function Ws(t){if(nr(t))return{message:t.message,name:t.name,stack:t.stack,...xr(t)};if(rn(t)){const e={type:t.type,target:Lr(t.target),currentTarget:Lr(t.currentTarget),...xr(t)};return typeof CustomEvent<"u"&&et(t,CustomEvent)&&(e.detail=t.detail),e}else return t}function Lr(t){try{return Fo(t)?K(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function xr(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}else return{}}function qo(t,e=40){const n=Object.keys(Ws(t));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=e)return We(r,e);for(let s=n.length;s>0;s--){const i=n.slice(0,s).join(", ");if(!(i.length>e))return s===n.length?i:We(i,e)}return""}function Go(){const t=T;return t.crypto||t.msCrypto}function G(t=Go()){let e=()=>Math.random()*16;try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(e()&15)>>n/4).toString(16))}function zs(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function yt(t){const{message:e,event_id:n}=t;if(e)return e;const r=zs(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Pn(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type="Error")}function Bt(t,e){const n=zs(t);if(!n)return;const r={type:"generic",handled:!0},s=n.mechanism;if(n.mechanism={...r,...s,...e},e&&"data"in e){const i={...s==null?void 0:s.data,...e.data};n.mechanism.data=i}}function $r(t){if(Wo(t))return!0;try{B(t,"__sentry_captured__",!0)}catch{}return!1}function Wo(t){try{return t.__sentry_captured__}catch{}}const Vs=1e3;function Nt(){return Date.now()/Vs}function zo(){const{performance:t}=T;if(!(t!=null&&t.now)||!t.timeOrigin)return Nt;const e=t.timeOrigin;return()=>(e+t.now())/Vs}let Mr;function $(){return(Mr??(Mr=zo()))()}let _n;function Vo(){var d;const{performance:t}=T;if(!(t!=null&&t.now))return[void 0,"none"];const e=3600*1e3,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,i=s<e,o=(d=t.timing)==null?void 0:d.navigationStart,a=typeof o=="number"?Math.abs(o+n-r):e,u=a<e;return i||u?s<=a?[t.timeOrigin,"timeOrigin"]:[o,"navigationStart"]:[r,"dateNow"]}function U(){return _n||(_n=Vo()),_n[0]}function Yo(t){const e=$(),n={sid:G(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>Jo(n)};return t&&Ut(n,t),n}function Ut(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||$(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:G()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function Xo(t,e){let n={};t.status==="ok"&&(n={status:"exited"}),Ut(t,n)}function Jo(t){return{sid:`${t.sid}`,init:t.init,started:new Date(t.started*1e3).toISOString(),timestamp:new Date(t.timestamp*1e3).toISOString(),status:t.status,errors:t.errors,did:typeof t.did=="number"||typeof t.did=="string"?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}}}function ge(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=ge(r[s],e[s],n-1));return r}function dt(){return G()}function _e(){return G().substring(16)}const On="_sentrySpan";function jt(t,e){e?B(t,On,e):delete t[On]}function ze(t){return t[On]}const Ko=100;class nt{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:dt(),sampleRand:Math.random()}}clone(){const e=new nt;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,jt(e,ze(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Ut(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof nt?n.getScopeData():ue(n)?e:void 0,{tags:s,extra:i,user:o,contexts:c,level:a,fingerprint:u=[],propagationContext:d}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...i},this._contexts={...this._contexts,...c},o&&Object.keys(o).length&&(this._user=o),a&&(this._level=a),u.length&&(this._fingerprint=u),d&&(this._propagationContext=d),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,jt(this,void 0),this._attachments=[],this.setPropagationContext({traceId:dt(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var i;const r=typeof n=="number"?n:Ko;if(r<=0)return this;const s={timestamp:Nt(),...e,message:e.message?We(e.message,2048):e.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(i=this._client)==null||i.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:ze(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=ge(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||G();if(!this._client)return g&&m.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=(r==null?void 0:r.event_id)||G();if(!this._client)return g&&m.warn("No client configured on scope - will not capture message!"),s;const i=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:i,...r,event_id:s},this),s}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||G();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(g&&m.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}function Zo(){return pe("defaultCurrentScope",()=>new nt)}function Qo(){return pe("defaultIsolationScope",()=>new nt)}class ta{constructor(e,n){let r;e?r=e:r=new nt;let s;n?s=n:s=new nt,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return me(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function qt(){const t=kt(),e=nn(t);return e.stack=e.stack||new ta(Zo(),Qo())}function ea(t){return qt().withScope(t)}function na(t,e){const n=qt();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function Dr(t){return qt().withScope(()=>t(qt().getIsolationScope()))}function ra(){return{withIsolationScope:Dr,withScope:ea,withSetScope:na,withSetIsolationScope:(t,e)=>Dr(e),getCurrentScope:()=>qt().getScope(),getIsolationScope:()=>qt().getIsolationScope()}}function Yt(t){const e=nn(t);return e.acs?e.acs:ra()}function v(){const t=kt();return Yt(t).getCurrentScope()}function Ct(){const t=kt();return Yt(t).getIsolationScope()}function sa(){return pe("globalScope",()=>new nt)}function sn(...t){const e=kt(),n=Yt(e);if(t.length===2){const[r,s]=t;return r?n.withSetScope(r,s):n.withScope(s)}return n.withScope(t[0])}function I(){return v().getClient()}function ia(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=e,i={trace_id:n,span_id:s||_e()};return r&&(i.parent_span_id=r),i}const J="sentry.source",or="sentry.sample_rate",Ys="sentry.previous_trace_sample_rate",rt="sentry.op",L="sentry.origin",Ve="sentry.idle_span_finish_reason",Se="sentry.measurement_unit",ye="sentry.measurement_value",Fr="sentry.custom_span_name",ar="sentry.profile_id",Xt="sentry.exclusive_time",oa="sentry.link.type",aa=0,Xs=1,O=2;function ca(t){if(t<400&&t>=100)return{code:Xs};if(t>=400&&t<500)switch(t){case 401:return{code:O,message:"unauthenticated"};case 403:return{code:O,message:"permission_denied"};case 404:return{code:O,message:"not_found"};case 409:return{code:O,message:"already_exists"};case 413:return{code:O,message:"failed_precondition"};case 429:return{code:O,message:"resource_exhausted"};case 499:return{code:O,message:"cancelled"};default:return{code:O,message:"invalid_argument"}}if(t>=500&&t<600)switch(t){case 501:return{code:O,message:"unimplemented"};case 503:return{code:O,message:"unavailable"};case 504:return{code:O,message:"deadline_exceeded"};default:return{code:O,message:"internal_error"}}return{code:O,message:"unknown_error"}}function Js(t,e){t.setAttribute("http.response.status_code",e);const n=ca(e);n.message!=="unknown_error"&&t.setStatus(n)}const Ks="_sentryScope",Zs="_sentryIsolationScope";function ua(t,e,n){t&&(B(t,Zs,n),B(t,Ks,e))}function Ye(t){return{scope:t[Ks],isolationScope:t[Zs]}}const cr="sentry-",da=/^sentry-/,fa=8192;function Qs(t){const e=pa(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,i])=>{if(s.match(da)){const o=s.slice(cr.length);r[o]=i}return r},{});if(Object.keys(n).length>0)return n}function la(t){if(!t)return;const e=Object.entries(t).reduce((n,[r,s])=>(s&&(n[`${cr}${r}`]=s),n),{});return ma(e)}function pa(t){if(!(!t||!Q(t)&&!Array.isArray(t)))return Array.isArray(t)?t.reduce((e,n)=>{const r=Hr(n);return Object.entries(r).forEach(([s,i])=>{e[s]=i}),e},{}):Hr(t)}function Hr(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}function ma(t){if(Object.keys(t).length!==0)return Object.entries(t).reduce((e,[n,r],s)=>{const i=`${encodeURIComponent(n)}=${encodeURIComponent(r)}`,o=s===0?i:`${e},${i}`;return o.length>fa?(g&&m.warn(`Not adding key: ${n} with val: ${r} to baggage header due to exceeding baggage size limits.`),e):o},"")}const ha=/^o(\d+)\./,ga=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function _a(t){return t==="http"||t==="https"}function Ee(t,e=!1){const{host:n,path:r,pass:s,port:i,projectId:o,protocol:c,publicKey:a}=t;return`${c}://${a}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function Sa(t){const e=ga.exec(t);if(!e){zt(()=>{console.error(`Invalid Sentry Dsn: ${t}`)});return}const[n,r,s="",i="",o="",c=""]=e.slice(1);let a="",u=c;const d=u.split("/");if(d.length>1&&(a=d.slice(0,-1).join("/"),u=d.pop()),u){const f=u.match(/^\d+/);f&&(u=f[0])}return ti({host:i,pass:s,path:a,projectId:u,port:o,protocol:n,publicKey:r})}function ti(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function ya(t){if(!g)return!0;const{port:e,projectId:n,protocol:r}=t;return["protocol","publicKey","host","projectId"].find(o=>t[o]?!1:(m.error(`Invalid Sentry Dsn: ${o} missing`),!0))?!1:n.match(/^\d+$/)?_a(r)?e&&isNaN(parseInt(e,10))?(m.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):!0:(m.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(m.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function Ea(t){const e=t.match(ha);return e==null?void 0:e[1]}function ba(t){const e=t.getOptions(),{host:n}=t.getDsn()||{};let r;return e.orgId?r=String(e.orgId):n&&(r=Ea(n)),r}function Ta(t){const e=typeof t=="string"?Sa(t):ti(t);if(!(!e||!ya(e)))return e}function de(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;if(!(typeof e!="number"||isNaN(e)||e<0||e>1))return e}const ei=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function va(t){if(!t)return;const e=t.match(ei);if(!e)return;let n;return e[3]==="1"?n=!0:e[3]==="0"&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}function Ia(t,e){const n=va(t),r=Qs(e);if(!(n!=null&&n.traceId))return{traceId:dt(),sampleRand:Math.random()};const s=Ra(n,r);r&&(r.sample_rand=s.toString());const{traceId:i,parentSpanId:o,parentSampled:c}=n;return{traceId:i,parentSpanId:o,sampled:c,dsc:r||{},sampleRand:s}}function ni(t=dt(),e=_e(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}function Ra(t,e){const n=de(e==null?void 0:e.sample_rand);if(n!==void 0)return n;const r=de(e==null?void 0:e.sample_rate);return r&&(t==null?void 0:t.parentSampled)!==void 0?t.parentSampled?Math.random()*r:r+Math.random()*(1-r):Math.random()}const ri=0,ur=1;let Br=!1;function wa(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:i,status:o,origin:c,links:a}=b(t);return{parent_span_id:i,span_id:e,trace_id:n,data:r,op:s,status:o,origin:c,links:a}}function Aa(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),s=r?e:b(t).parent_span_id,i=Ye(t).scope,o=r?(i==null?void 0:i.getPropagationContext().propagationSpanId)||_e():e;return{parent_span_id:s,span_id:o,trace_id:n}}function ka(t){const{traceId:e,spanId:n}=t.spanContext(),r=Pt(t);return ni(e,n,r)}function si(t){if(t&&t.length>0)return t.map(({context:{spanId:e,traceId:n,traceFlags:r,...s},attributes:i})=>({span_id:e,trace_id:n,sampled:r===ur,attributes:i,...s}))}function Tt(t){return typeof t=="number"?Ur(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Ur(t.getTime()):$()}function Ur(t){return t>9999999999?t/1e3:t}function b(t){var r;if(Ca(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(Na(t)){const{attributes:s,startTime:i,name:o,endTime:c,status:a,links:u}=t,d="parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0;return{span_id:e,trace_id:n,data:s,description:o,parent_span_id:d,start_timestamp:Tt(i),timestamp:Tt(c)||void 0,status:ii(a),op:s[rt],origin:s[L],links:si(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function Na(t){const e=t;return!!e.attributes&&!!e.startTime&&!!e.name&&!!e.endTime&&!!e.status}function Ca(t){return typeof t.getSpanJSON=="function"}function Pt(t){const{traceFlags:e}=t.spanContext();return e===ur}function ii(t){if(!(!t||t.code===aa))return t.code===Xs?"ok":t.message||"unknown_error"}const vt="_sentryChildSpans",Ln="_sentryRootSpan";function oi(t,e){const n=t[Ln]||t;B(e,Ln,n),t[vt]?t[vt].add(e):B(t,vt,new Set([e]))}function Pa(t,e){t[vt]&&t[vt].delete(e)}function Fe(t){const e=new Set;function n(r){if(!e.has(r)&&Pt(r)){e.add(r);const s=r[vt]?Array.from(r[vt]):[];for(const i of s)n(i)}}return n(t),Array.from(e)}function D(t){return t[Ln]||t}function j(){const t=kt(),e=Yt(t);return e.getActiveSpan?e.getActiveSpan():ze(v())}function xn(){Br||(zt(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Br=!0)}let jr=!1;function Oa(){if(jr)return;function t(){const e=j(),n=e&&D(e);if(n){const r="internal_error";g&&m.log(`[Tracing] Root span: ${r} -> Global error occurred`),n.setStatus({code:O,message:r})}}t.tag="sentry_tracingErrorCallback",jr=!0,Ds(t),Fs(t)}function ft(t){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=t||((n=I())==null?void 0:n.getOptions());return!!e&&(e.tracesSampleRate!=null||!!e.tracesSampler)}const dr="production",ai="_frozenDsc";function He(t,e){B(t,ai,e)}function ci(t,e){const n=e.getOptions(),{publicKey:r}=e.getDsn()||{},s={environment:n.environment||dr,release:n.release,public_key:r,trace_id:t,org_id:ba(e)};return e.emit("createDsc",s),s}function ui(t,e){const n=e.getPropagationContext();return n.dsc||ci(n.traceId,t)}function lt(t){var h;const e=I();if(!e)return{};const n=D(t),r=b(n),s=r.data,i=n.spanContext().traceState,o=(i==null?void 0:i.get("sentry.sample_rate"))??s[or]??s[Ys];function c(_){return(typeof o=="number"||typeof o=="string")&&(_.sample_rate=`${o}`),_}const a=n[ai];if(a)return c(a);const u=i==null?void 0:i.get("sentry.dsc"),d=u&&Qs(u);if(d)return c(d);const f=ci(t.spanContext().traceId,e),l=s[J],p=r.description;return l!=="url"&&p&&(f.transaction=p),ft()&&(f.sampled=String(Pt(n)),f.sample_rand=(i==null?void 0:i.get("sentry.sample_rand"))??((h=Ye(n).scope)==null?void 0:h.getPropagationContext().sampleRand.toString())),c(f),e.emit("createDsc",f,n),f}class pt{constructor(e={}){this._traceId=e.traceId||dt(),this._spanId=e.spanId||_e()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:ri}}end(e){}setAttribute(e,n){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,n,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,n){}}function Z(t,e=100,n=1/0){try{return $n("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function di(t,e=3,n=100*1024){const r=Z(t,e);return Ma(r)>n?di(t,e-1,n):r}function $n(t,e,n=1/0,r=1/0,s=Da()){const[i,o]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const c=La(t,e);if(!c.startsWith("[object "))return c;if(e.__sentry_skip_normalization__)return e;const a=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(a===0)return c.replace("object ","");if(i(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{const p=u.toJSON();return $n("",p,a-1,r,s)}catch{}const d=Array.isArray(e)?[]:{};let f=0;const l=Ws(e);for(const p in l){if(!Object.prototype.hasOwnProperty.call(l,p))continue;if(f>=r){d[p]="[MaxProperties ~]";break}const h=l[p];d[p]=$n(p,h,a-1,r,s),f++}return o(e),d}function La(t,e){try{if(t==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(t==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(Us(e))return"[VueViewModel]";if(Bo(e))return"[SyntheticEvent]";if(typeof e=="number"&&!Number.isFinite(e))return`[${e}]`;if(typeof e=="function")return`[Function: ${tt(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;const n=xa(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function xa(t){const e=Object.getPrototypeOf(t);return e!=null&&e.constructor?e.constructor.name:"null prototype"}function $a(t){return~-encodeURI(t).split(/%..|./).length}function Ma(t){return $a(JSON.stringify(t))}function Da(){const t=new WeakSet;function e(r){return t.has(r)?!0:(t.add(r),!1)}function n(r){t.delete(r)}return[e,n]}function Jt(t,e=[]){return[t,e]}function Fa(t,e){const[n,r]=t;return[n,[...r,e]]}function qr(t,e){const n=t[1];for(const r of n){const s=r[0].type;if(e(r,s))return!0}return!1}function Mn(t){const e=nn(T);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function Ha(t){const[e,n]=t;let r=JSON.stringify(e);function s(i){typeof r=="string"?r=typeof i=="string"?r+i:[Mn(r),i]:r.push(typeof i=="string"?Mn(i):i)}for(const i of n){const[o,c]=i;if(s(`
${JSON.stringify(o)}
`),typeof c=="string"||c instanceof Uint8Array)s(c);else{let a;try{a=JSON.stringify(c)}catch{a=JSON.stringify(Z(c))}s(a)}}return typeof r=="string"?r:Ba(r)}function Ba(t){const e=t.reduce((s,i)=>s+i.length,0),n=new Uint8Array(e);let r=0;for(const s of t)n.set(s,r),r+=s.length;return n}function Ua(t){return[{type:"span"},t]}function ja(t){const e=typeof t.data=="string"?Mn(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const qa={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function Gr(t){return qa[t]}function fi(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function Ga(t,e,n,r){var i;const s=(i=t.sdkProcessingMetadata)==null?void 0:i.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:Ee(r)},...s&&{trace:s}}}function Dn(t,e){if(!(e!=null&&e.length)||!t.description)return!1;for(const n of e){if(za(n)){if(De(t.description,n))return!0;continue}if(!n.name&&!n.op)continue;const r=n.name?De(t.description,n.name):!0,s=n.op?t.op&&De(t.op,n.op):!0;if(r&&s)return!0}return!1}function Wa(t,e){const n=e.parent_span_id,r=e.span_id;if(n)for(const s of t)s.parent_span_id===r&&(s.parent_span_id=n)}function za(t){return typeof t=="string"||t instanceof RegExp}function Va(t,e){var r,s,i,o;if(!e)return t;const n=t.sdk||{};return t.sdk={...n,name:n.name||e.name,version:n.version||e.version,integrations:[...((r=t.sdk)==null?void 0:r.integrations)||[],...e.integrations||[]],packages:[...((s=t.sdk)==null?void 0:s.packages)||[],...e.packages||[]],settings:(i=t.sdk)!=null&&i.settings||e.settings?{...(o=t.sdk)==null?void 0:o.settings,...e.settings}:void 0},t}function Ya(t,e,n,r){const s=fi(n),i={sent_at:new Date().toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:Ee(e)}},o="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return Jt(i,[o])}function Xa(t,e,n,r){const s=fi(n),i=t.type&&t.type!=="replay_event"?t.type:"event";Va(t,n==null?void 0:n.sdk);const o=Ga(t,s,r,e);return delete t.sdkProcessingMetadata,Jt(o,[[{type:i},t]])}function Ja(t,e){function n(p){return!!p.trace_id&&!!p.public_key}const r=lt(t[0]),s=e==null?void 0:e.getDsn(),i=e==null?void 0:e.getOptions().tunnel,o={sent_at:new Date().toISOString(),...n(r)&&{trace:r},...!!i&&s&&{dsn:Ee(s)}},{beforeSendSpan:c,ignoreSpans:a}=(e==null?void 0:e.getOptions())||{},u=a!=null&&a.length?t.filter(p=>!Dn(b(p),a)):t,d=t.length-u.length;d&&(e==null||e.recordDroppedEvent("before_send","span",d));const f=c?p=>{const h=b(p),_=c(h);return _||(xn(),h)}:b,l=[];for(const p of u){const h=f(p);h&&l.push(Ua(h))}return Jt(o,l)}function Ka(t){if(!g)return;const{description:e="< unknown name >",op:n="< unknown op >",parent_span_id:r}=b(t),{spanId:s}=t.spanContext(),i=Pt(t),o=D(t),c=o===t,a=`[Tracing] Starting ${i?"sampled":"unsampled"} ${c?"root ":""}span`,u=[`op: ${n}`,`name: ${e}`,`ID: ${s}`];if(r&&u.push(`parent ID: ${r}`),!c){const{op:d,description:f}=b(o);u.push(`root ID: ${o.spanContext().spanId}`),d&&u.push(`root op: ${d}`),f&&u.push(`root description: ${f}`)}m.log(`${a}
  ${u.join(`
  `)}`)}function Za(t){if(!g)return;const{description:e="< unknown name >",op:n="< unknown op >"}=b(t),{spanId:r}=t.spanContext(),i=D(t)===t,o=`[Tracing] Finishing "${n}" ${i?"root ":""}span "${e}" with ID ${r}`;m.log(o)}function Qa(t,e,n,r=j()){const s=r&&D(r);s&&(g&&m.log(`[Measurement] Setting measurement on root span: ${t} = ${e} ${n}`),s.addEvent(t,{[ye]:e,[Se]:n}))}function Wr(t){if(!t||t.length===0)return;const e={};return t.forEach(n=>{const r=n.attributes||{},s=r[Se],i=r[ye];typeof s=="string"&&typeof i=="number"&&(e[n.name]={value:i,unit:s})}),e}const zr=1e3;class on{constructor(e={}){this._traceId=e.traceId||dt(),this._spanId=e.spanId||_e(),this._startTime=e.startTimestamp||$(),this._links=e.links,this._attributes={},this.setAttributes({[L]:"manual",[rt]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,n){}spanContext(){const{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?ur:ri}}setAttribute(e,n){return n===void 0?delete this._attributes[e]:this._attributes[e]=n,this}setAttributes(e){return Object.keys(e).forEach(n=>this.setAttribute(n,e[n])),this}updateStartTime(e){this._startTime=Tt(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(J,"custom"),this}end(e){this._endTime||(this._endTime=Tt(e),Za(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[rt],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:ii(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[L],profile_id:this._attributes[ar],exclusive_time:this._attributes[Xt],measurements:Wr(this._events),is_segment:this._isStandaloneSpan&&D(this)===this||void 0,segment_id:this._isStandaloneSpan?D(this).spanContext().spanId:void 0,links:si(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,n,r){g&&m.log("[Tracing] Adding an event to span:",e);const s=Vr(n)?n:r||$(),i=Vr(n)?{}:n||{},o={name:e,time:Tt(s),attributes:i};return this._events.push(o),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=I();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===D(this)))return;if(this._isStandaloneSpan){this._sampled?ec(Ja([this],e)):(g&&m.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span"));return}const r=this._convertSpanToTransaction();r&&(Ye(this).scope||v()).captureEvent(r)}_convertSpanToTransaction(){var d;if(!Yr(b(this)))return;this._name||(g&&m.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:n}=Ye(this),r=(d=e==null?void 0:e.getScopeData().sdkProcessingMetadata)==null?void 0:d.normalizedRequest;if(this._sampled!==!0)return;const i=Fe(this).filter(f=>f!==this&&!tc(f)).map(f=>b(f)).filter(Yr),o=this._attributes[J];delete this._attributes[Fr],i.forEach(f=>{delete f.data[Fr]});const c={contexts:{trace:wa(this)},spans:i.length>zr?i.sort((f,l)=>f.start_timestamp-l.start_timestamp).slice(0,zr):i,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:lt(this)},request:r,...o&&{transaction_info:{source:o}}},a=Wr(this._events);return a&&Object.keys(a).length&&(g&&m.log("[Measurements] Adding measurements to transaction event",JSON.stringify(a,void 0,2)),c.measurements=a),c}}function Vr(t){return t&&typeof t=="number"||t instanceof Date||Array.isArray(t)}function Yr(t){return!!t.start_timestamp&&!!t.timestamp&&!!t.span_id&&!!t.trace_id}function tc(t){return t instanceof on&&t.isStandaloneSpan()}function ec(t){const e=I();if(!e)return;const n=t[1];if(!n||n.length===0){e.recordDroppedEvent("before_send","span");return}e.sendEnvelope(t)}function nc(t,e,n=()=>{}){let r;try{r=t()}catch(s){throw e(s),n(),s}return rc(r,e,n)}function rc(t,e,n){return me(t)?t.then(r=>(n(),r),r=>{throw e(r),n(),r}):(n(),t)}function sc(t,e,n){if(!ft(t))return[!1];let r,s;typeof t.tracesSampler=="function"?(s=t.tracesSampler({...e,inheritOrSampleWith:c=>typeof e.parentSampleRate=="number"?e.parentSampleRate:typeof e.parentSampled=="boolean"?Number(e.parentSampled):c}),r=!0):e.parentSampled!==void 0?s=e.parentSampled:typeof t.tracesSampleRate<"u"&&(s=t.tracesSampleRate,r=!0);const i=de(s);if(i===void 0)return g&&m.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(s)} of type ${JSON.stringify(typeof s)}.`),[!1];if(!i)return g&&m.log(`[Tracing] Discarding transaction because ${typeof t.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),[!1,i,r];const o=n<i;return o||g&&m.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(s)})`),[o,i,r]}const li="__SENTRY_SUPPRESS_TRACING__";function ic(t,e){const n=lr();if(n.startSpan)return n.startSpan(t,e);const r=mi(t),{forceTransaction:s,parentSpan:i,scope:o}=t,c=o==null?void 0:o.clone();return sn(c,()=>ac(i)(()=>{const u=v(),d=hi(u,i),l=t.onlyIfParent&&!d?new pt:pi({parentSpan:d,spanArguments:r,forceTransaction:s,scope:u});return jt(u,l),nc(()=>e(l),()=>{const{status:p}=b(l);l.isRecording()&&(!p||p==="ok")&&l.setStatus({code:O,message:"internal_error"})},()=>{l.end()})}))}function Kt(t){const e=lr();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=mi(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?o=>sn(t.scope,o):s!==void 0?o=>fr(s,o):o=>o())(()=>{const o=v(),c=hi(o,s);return t.onlyIfParent&&!c?new pt:pi({parentSpan:c,spanArguments:n,forceTransaction:r,scope:o})})}function fr(t,e){const n=lr();return n.withActiveSpan?n.withActiveSpan(t,e):sn(r=>(jt(r,t||void 0),e(r)))}function pi({parentSpan:t,spanArguments:e,forceTransaction:n,scope:r}){if(!ft()){const o=new pt;if(n||!t){const c={sampled:"false",sample_rate:"0",transaction:e.name,...lt(o)};He(o,c)}return o}const s=Ct();let i;if(t&&!n)i=oc(t,r,e),oi(t,i);else if(t){const o=lt(t),{traceId:c,spanId:a}=t.spanContext(),u=Pt(t);i=Xr({traceId:c,parentSpanId:a,...e},r,u),He(i,o)}else{const{traceId:o,dsc:c,parentSpanId:a,sampled:u}={...s.getPropagationContext(),...r.getPropagationContext()};i=Xr({traceId:o,parentSpanId:a,...e},r,u),c&&He(i,c)}return Ka(i),ua(i,r,s),i}function mi(t){const n={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){const r={...n};return r.startTimestamp=Tt(t.startTime),delete r.startTime,r}return n}function lr(){const t=kt();return Yt(t)}function Xr(t,e,n){var h;const r=I(),s=(r==null?void 0:r.getOptions())||{},{name:i=""}=t,o={spanAttributes:{...t.attributes},spanName:i,parentSampled:n};r==null||r.emit("beforeSampling",o,{decision:!1});const c=o.parentSampled??n,a=o.spanAttributes,u=e.getPropagationContext(),[d,f,l]=e.getScopeData().sdkProcessingMetadata[li]?[!1]:sc(s,{name:i,parentSampled:c,attributes:a,parentSampleRate:de((h=u.dsc)==null?void 0:h.sample_rate)},u.sampleRand),p=new on({...t,attributes:{[J]:"custom",[or]:f!==void 0&&l?f:void 0,...a},sampled:d});return!d&&r&&(g&&m.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",p),p}function oc(t,e,n){const{spanId:r,traceId:s}=t.spanContext(),i=e.getScopeData().sdkProcessingMetadata[li]?!1:Pt(t),o=i?new on({...n,parentSpanId:r,traceId:s,sampled:i}):new pt({traceId:s});oi(t,o);const c=I();return c&&(c.emit("spanStart",o),n.endTimestamp&&c.emit("spanEnd",o)),o}function hi(t,e){if(e)return e;if(e===null)return;const n=ze(t);if(!n)return;const r=I();return(r?r.getOptions():{}).parentSpanIsAlwaysRootSpan?D(n):n}function ac(t){return t!==void 0?e=>fr(t,e):e=>e()}const Be={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},cc="heartbeatFailed",uc="idleTimeout",dc="finalTimeout",fc="externalFinish";function gi(t,e={}){const n=new Map;let r=!1,s,i=fc,o=!e.disableAutoFinish;const c=[],{idleTimeout:a=Be.idleTimeout,finalTimeout:u=Be.finalTimeout,childSpanTimeout:d=Be.childSpanTimeout,beforeSpanEnd:f}=e,l=I();if(!l||!ft()){const E=new pt,M={sample_rate:"0",sampled:"false",...lt(E)};return He(E,M),E}const p=v(),h=j(),_=lc(t);_.end=new Proxy(_.end,{apply(E,M,Lt){if(f&&f(_),M instanceof pt)return;const[te,...xt]=Lt,St=te||$(),N=Tt(St),it=Fe(_).filter(P=>P!==_);if(!it.length)return Re(N),Reflect.apply(E,M,[N,...xt]);const ot=it.map(P=>b(P).timestamp).filter(P=>!!P),w=ot.length?Math.max(...ot):void 0,C=b(_).start_timestamp,R=Math.min(C?C+u/1e3:1/0,Math.max(C||-1/0,Math.min(N,w||1/0)));return Re(R),Reflect.apply(E,M,[R,...xt])}});function A(){s&&(clearTimeout(s),s=void 0)}function Y(E){A(),s=setTimeout(()=>{!r&&n.size===0&&o&&(i=uc,_.end(E))},a)}function st(E){s=setTimeout(()=>{!r&&o&&(i=cc,_.end(E))},d)}function Ot(E){A(),n.set(E,!0);const M=$();st(M+d/1e3)}function fn(E){if(n.has(E)&&n.delete(E),n.size===0){const M=$();Y(M+a/1e3)}}function Re(E){r=!0,n.clear(),c.forEach(N=>N()),jt(p,h);const M=b(_),{start_timestamp:Lt}=M;if(!Lt)return;M.data[Ve]||_.setAttribute(Ve,i),m.log(`[Tracing] Idle span "${M.op}" finished`);const xt=Fe(_).filter(N=>N!==_);let St=0;xt.forEach(N=>{N.isRecording()&&(N.setStatus({code:O,message:"cancelled"}),N.end(E),g&&m.log("[Tracing] Cancelling span since span ended early",JSON.stringify(N,void 0,2)));const it=b(N),{timestamp:ot=0,start_timestamp:w=0}=it,C=w<=E,R=(u+a)/1e3,P=ot-w<=R;if(g){const x=JSON.stringify(N,void 0,2);C?P||m.log("[Tracing] Discarding span since it finished after idle span final timeout",x):m.log("[Tracing] Discarding span since it happened after idle span was finished",x)}(!P||!C)&&(Pa(_,N),St++)}),St>0&&_.setAttribute("sentry.idle_span_discarded_spans",St)}return c.push(l.on("spanStart",E=>{if(r||E===_||b(E).timestamp||E instanceof on&&E.isStandaloneSpan())return;Fe(_).includes(E)&&Ot(E.spanContext().spanId)})),c.push(l.on("spanEnd",E=>{r||fn(E.spanContext().spanId)})),c.push(l.on("idleSpanEnableAutoFinish",E=>{E===_&&(o=!0,Y(),n.size&&st())})),e.disableAutoFinish||Y(),setTimeout(()=>{r||(_.setStatus({code:O,message:"deadline_exceeded"}),i=dc,_.end())},u),_}function lc(t){const e=Kt(t);return jt(v(),e),g&&m.log("[Tracing] Started span is an idle span"),e}const Sn=0,Jr=1,Kr=2;function wt(t){return new mt(e=>{e(t)})}function Xe(t){return new mt((e,n)=>{n(t)})}class mt{constructor(e){this._state=Sn,this._handlers=[],this._runExecutor(e)}then(e,n){return new mt((r,s)=>{this._handlers.push([!1,i=>{if(!e)r(i);else try{r(e(i))}catch(o){s(o)}},i=>{if(!n)s(i);else try{r(n(i))}catch(o){s(o)}}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new mt((n,r)=>{let s,i;return this.then(o=>{i=!1,s=o,e&&e()},o=>{i=!0,s=o,e&&e()}).then(()=>{if(i){r(s);return}n(s)})})}_executeHandlers(){if(this._state===Sn)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===Jr&&n[1](this._value),this._state===Kr&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(i,o)=>{if(this._state===Sn){if(me(o)){o.then(r,s);return}this._state=i,this._value=o,this._executeHandlers()}},r=i=>{n(Jr,i)},s=i=>{n(Kr,i)};try{e(r,s)}catch(i){s(i)}}}function Fn(t,e,n,r=0){return new mt((s,i)=>{const o=t[r];if(e===null||typeof o!="function")s(e);else{const c=o({...e},n);g&&o.id&&c===null&&m.log(`Event processor "${o.id}" dropped event`),me(c)?c.then(a=>Fn(t,a,n,r+1).then(s)).then(null,i):Fn(t,c,n,r+1).then(s).then(null,i)}})}function pc(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:i}=e;mc(t,e),r&&_c(t,r),Sc(t,n),hc(t,s),gc(t,i)}function Zr(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,sdkProcessingMetadata:c,breadcrumbs:a,fingerprint:u,eventProcessors:d,attachments:f,propagationContext:l,transactionName:p,span:h}=e;Ae(t,"extra",n),Ae(t,"tags",r),Ae(t,"user",s),Ae(t,"contexts",i),t.sdkProcessingMetadata=ge(t.sdkProcessingMetadata,c,2),o&&(t.level=o),p&&(t.transactionName=p),h&&(t.span=h),a.length&&(t.breadcrumbs=[...t.breadcrumbs,...a]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),f.length&&(t.attachments=[...t.attachments,...f]),t.propagationContext={...t.propagationContext,...l}}function Ae(t,e,n){t[e]=ge(t[e],n,1)}function mc(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,transactionName:c}=e;Object.keys(n).length&&(t.extra={...n,...t.extra}),Object.keys(r).length&&(t.tags={...r,...t.tags}),Object.keys(s).length&&(t.user={...s,...t.user}),Object.keys(i).length&&(t.contexts={...i,...t.contexts}),o&&(t.level=o),c&&t.type!=="transaction"&&(t.transaction=c)}function hc(t,e){const n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}function gc(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}function _c(t,e){t.contexts={trace:Aa(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:lt(e),...t.sdkProcessingMetadata};const n=D(e),r=b(n).description;r&&!t.transaction&&t.type==="transaction"&&(t.transaction=r)}function Sc(t,e){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],e&&(t.fingerprint=t.fingerprint.concat(e)),t.fingerprint.length||delete t.fingerprint}let ke,Qr,Ne;function yc(t){const e=T._sentryDebugIds;if(!e)return{};const n=Object.keys(e);return Ne&&n.length===Qr||(Qr=n.length,Ne=n.reduce((r,s)=>{ke||(ke={});const i=ke[s];if(i)r[i[0]]=i[1];else{const o=t(s);for(let c=o.length-1;c>=0;c--){const a=o[c],u=a==null?void 0:a.filename,d=e[s];if(u&&d){r[u]=d,ke[s]=[u,d];break}}}return r},{})),Ne}function Ec(t,e,n,r,s,i){const{normalizeDepth:o=3,normalizeMaxBreadth:c=1e3}=t,a={...e,event_id:e.event_id||n.event_id||G(),timestamp:e.timestamp||Nt()},u=n.integrations||t.integrations.map(A=>A.name);bc(a,t),Ic(a,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&Tc(a,t.stackParser);const d=wc(r,n.captureContext);n.mechanism&&Bt(a,n.mechanism);const f=s?s.getEventProcessors():[],l=sa().getScopeData();if(i){const A=i.getScopeData();Zr(l,A)}if(d){const A=d.getScopeData();Zr(l,A)}const p=[...n.attachments||[],...l.attachments];p.length&&(n.attachments=p),pc(a,l);const h=[...f,...l.eventProcessors];return Fn(h,a,n).then(A=>(A&&vc(A),typeof o=="number"&&o>0?Rc(A,o,c):A))}function bc(t,e){const{environment:n,release:r,dist:s,maxValueLength:i=250}=e;t.environment=t.environment||n||dr,!t.release&&r&&(t.release=r),!t.dist&&s&&(t.dist=s);const o=t.request;o!=null&&o.url&&(o.url=We(o.url,i))}function Tc(t,e){var r,s;const n=yc(e);(s=(r=t.exception)==null?void 0:r.values)==null||s.forEach(i=>{var o,c;(c=(o=i.stacktrace)==null?void 0:o.frames)==null||c.forEach(a=>{a.filename&&(a.debug_id=n[a.filename])})})}function vc(t){var r,s;const e={};if((s=(r=t.exception)==null?void 0:r.values)==null||s.forEach(i=>{var o,c;(c=(o=i.stacktrace)==null?void 0:o.frames)==null||c.forEach(a=>{a.debug_id&&(a.abs_path?e[a.abs_path]=a.debug_id:a.filename&&(e[a.filename]=a.debug_id),delete a.debug_id)})}),Object.keys(e).length===0)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.entries(e).forEach(([i,o])=>{n.push({type:"sourcemap",code_file:i,debug_id:o})})}function Ic(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}function Rc(t,e,n){var s,i;if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(o=>({...o,...o.data&&{data:Z(o.data,e,n)}}))},...t.user&&{user:Z(t.user,e,n)},...t.contexts&&{contexts:Z(t.contexts,e,n)},...t.extra&&{extra:Z(t.extra,e,n)}};return(s=t.contexts)!=null&&s.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=Z(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(o=>({...o,...o.data&&{data:Z(o.data,e,n)}}))),(i=t.contexts)!=null&&i.flags&&r.contexts&&(r.contexts.flags=Z(t.contexts.flags,3,n)),r}function wc(t,e){if(!e)return t;const n=t?t.clone():new nt;return n.update(e),n}function Ac(t,e){return v().captureException(t,void 0)}function _i(t,e){return v().captureEvent(t,e)}function kc(){const t=I();return(t==null?void 0:t.getOptions().enabled)!==!1&&!!(t!=null&&t.getTransport())}function ts(t){const e=Ct(),n=v(),{userAgent:r}=T.navigator||{},s=Yo({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),i=e.getSession();return(i==null?void 0:i.status)==="ok"&&Ut(i,{status:"exited"}),Si(),e.setSession(s),s}function Si(){const t=Ct(),n=v().getSession()||t.getSession();n&&Xo(n),yi(),t.setSession()}function yi(){const t=Ct(),e=I(),n=t.getSession();n&&e&&e.captureSession(n)}function es(t=!1){if(t){Si();return}yi()}const Nc="7";function Cc(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function Pc(t){return`${Cc(t)}${t.projectId}/envelope/`}function Oc(t,e){const n={sentry_version:Nc};return t.publicKey&&(n.sentry_key=t.publicKey),e&&(n.sentry_client=`${e.name}/${e.version}`),new URLSearchParams(n).toString()}function Lc(t,e,n){return e||`${Pc(t)}?${Oc(t,n)}`}const ns=[];function xc(t){const e={};return t.forEach(n=>{const{name:r}=n,s=e[r];s&&!s.isDefaultInstance&&n.isDefaultInstance||(e[r]=n)}),Object.values(e)}function $c(t){const e=t.defaultIntegrations||[],n=t.integrations;e.forEach(s=>{s.isDefaultInstance=!0});let r;if(Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const s=n(e);r=Array.isArray(s)?s:[s]}else r=e;return xc(r)}function Mc(t,e){const n={};return e.forEach(r=>{r&&Ei(t,r,n)}),n}function rs(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function Ei(t,e,n){if(n[e.name]){g&&m.log(`Integration skipped because it was already installed: ${e.name}`);return}if(n[e.name]=e,ns.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),ns.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,i)=>r(s,i,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((i,o)=>r(i,o,t),{id:e.name});t.addEventProcessor(s)}g&&m.log(`Integration installed: ${e.name}`)}function Dc(t,e,n){const r=[{type:"client_report"},{timestamp:Nt(),discarded_events:t}];return Jt(e?{dsn:e}:{},[r])}function bi(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}function Fc(t){var a;const{trace_id:e,parent_span_id:n,span_id:r,status:s,origin:i,data:o,op:c}=((a=t.contexts)==null?void 0:a.trace)??{};return{data:o??{},description:t.transaction,op:c,parent_span_id:n,span_id:r??"",start_timestamp:t.start_timestamp??0,status:s,timestamp:t.timestamp,trace_id:e??"",origin:i,profile_id:o==null?void 0:o[ar],exclusive_time:o==null?void 0:o[Xt],measurements:t.measurements,is_segment:!0}}function Hc(t){return{type:"transaction",timestamp:t.timestamp,start_timestamp:t.start_timestamp,transaction:t.description,contexts:{trace:{trace_id:t.trace_id,span_id:t.span_id,parent_span_id:t.parent_span_id,op:t.op,status:t.status,origin:t.origin,data:{...t.data,...t.profile_id&&{[ar]:t.profile_id},...t.exclusive_time&&{[Xt]:t.exclusive_time}}}},measurements:t.measurements}}const ss="Not capturing exception because it's already been captured.",is="Discarded session because of missing or non-string release",Ti=Symbol.for("SentryInternalError"),vi=Symbol.for("SentryDoNotSendEventError");function Ue(t){return{message:t,[Ti]:!0}}function yn(t){return{message:t,[vi]:!0}}function os(t){return!!t&&typeof t=="object"&&Ti in t}function as(t){return!!t&&typeof t=="object"&&vi in t}class Bc{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Ta(e.dsn):g&&m.warn("No DSN provided, client will not send events."),this._dsn){const n=Lc(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=G();if($r(e))return g&&m.log(ss),s;const i={event_id:s,...n};return this._process(this.eventFromException(e,i).then(o=>this._captureEvent(o,i,r))),i.event_id}captureMessage(e,n,r,s){const i={event_id:G(),...r},o=rr(e)?e:String(e),c=ce(e)?this.eventFromMessage(o,n,i):this.eventFromException(e,i);return this._process(c.then(a=>this._captureEvent(a,i,s))),i.event_id}captureEvent(e,n,r){const s=G();if(n!=null&&n.originalException&&$r(n.originalException))return g&&m.log(ss),s;const i={event_id:s,...n},o=e.sdkProcessingMetadata||{},c=o.capturedSpanScope,a=o.capturedSpanIsolationScope;return this._process(this._captureEvent(e,i,c||r,a)),i.event_id}captureSession(e){this.sendSession(e),Ut(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):wt(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];Ei(this,e,this._integrations),n||rs(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=Xa(e,this._dsn,this._options._metadata,this._options.tunnel);for(const i of n.attachments||[])r=Fa(r,ja(i));const s=this.sendEnvelope(r);s&&s.then(i=>this.emit("afterSendEvent",e,i),null)}sendSession(e){const{release:n,environment:r=dr}=this._options;if("aggregates"in e){const i=e.attrs||{};if(!i.release&&!n){g&&m.warn(is);return}i.release=i.release||n,i.environment=i.environment||r,e.attrs=i}else{if(!e.release&&!n){g&&m.warn(is);return}e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const s=Ya(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const s=`${e}:${n}`;g&&m.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>(g&&m.error("Error while sending envelope:",n),n)):(g&&m.error("Transport disabled"),wt({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=Mc(this,e),rs(this,e)}_updateSessionFromEvent(e,n){var a;let r=n.level==="fatal",s=!1;const i=(a=n.exception)==null?void 0:a.values;if(i){s=!0;for(const u of i){const d=u.mechanism;if((d==null?void 0:d.handled)===!1){r=!0;break}}}const o=e.status==="ok";(o&&e.errors===0||o&&r)&&(Ut(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new mt(n=>{let r=0;const s=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=s,e&&r>=e&&(clearInterval(i),n(!1)))},s)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s){const i=this.getOptions(),o=Object.keys(this._integrations);return!n.integrations&&(o!=null&&o.length)&&(n.integrations=o),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),Ec(i,e,n,r,this,s).then(c=>{if(c===null)return c;this.emit("postprocessEvent",c,n),c.contexts={trace:ia(r),...c.contexts};const a=ui(this,r);return c.sdkProcessingMetadata={dynamicSamplingContext:a,...c.sdkProcessingMetadata},c})}_captureEvent(e,n={},r=v(),s=Ct()){return g&&Hn(e)&&m.log(`Captured error event \`${bi(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,s).then(i=>i.event_id,i=>{g&&(as(i)?m.log(i.message):os(i)?m.warn(i.message):m.warn(i))})}_processEvent(e,n,r,s){const i=this.getOptions(),{sampleRate:o}=i,c=Ii(e),a=Hn(e),u=e.type||"error",d=`before send for type \`${u}\``,f=typeof o>"u"?void 0:de(o);if(a&&typeof f=="number"&&Math.random()>f)return this.recordDroppedEvent("sample_rate","error"),Xe(yn(`Discarding event because it's not included in the random sample (sampling rate = ${o})`));const l=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,s).then(p=>{if(p===null)throw this.recordDroppedEvent("event_processor",l),yn("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return p;const _=jc(this,i,p,n);return Uc(_,d)}).then(p=>{var A;if(p===null){if(this.recordDroppedEvent("before_send",l),c){const st=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",st)}throw yn(`${d} returned \`null\`, will not send event.`)}const h=r.getSession()||s.getSession();if(a&&h&&this._updateSessionFromEvent(h,p),c){const Y=((A=p.sdkProcessingMetadata)==null?void 0:A.spanCountBeforeProcessing)||0,st=p.spans?p.spans.length:0,Ot=Y-st;Ot>0&&this.recordDroppedEvent("before_send","span",Ot)}const _=p.transaction_info;if(c&&_&&p.transaction!==e.transaction){const Y="custom";p.transaction_info={..._,source:Y}}return this.sendEvent(p,n),p}).then(null,p=>{throw as(p)||os(p)?p:(this.captureException(p,{mechanism:{handled:!1,type:"internal"},data:{__sentry__:!0},originalException:p}),Ue(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${p}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,i]=n.split(":");return{reason:s,category:i,quantity:r}})}_flushOutcomes(){g&&m.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0){g&&m.log("No outcomes to send");return}if(!this._dsn){g&&m.log("No dsn provided, will not send outcomes");return}g&&m.log("Sending outcomes:",e);const n=Dc(e,this._options.tunnel&&Ee(this._dsn));this.sendEnvelope(n)}}function Uc(t,e){const n=`${e} must return \`null\` or a valid event.`;if(me(t))return t.then(r=>{if(!ue(r)&&r!==null)throw Ue(n);return r},r=>{throw Ue(`${e} rejected with ${r}`)});if(!ue(t)&&t!==null)throw Ue(n);return t}function jc(t,e,n,r){const{beforeSend:s,beforeSendTransaction:i,beforeSendSpan:o,ignoreSpans:c}=e;let a=n;if(Hn(a)&&s)return s(a,r);if(Ii(a)){if(o||c){const u=Fc(a);if(c!=null&&c.length&&Dn(u,c))return null;if(o){const d=o(u);d?a=ge(n,Hc(d)):xn()}if(a.spans){const d=[],f=a.spans;for(const p of f){if(c!=null&&c.length&&Dn(p,c)){Wa(f,p);continue}if(o){const h=o(p);h?d.push(h):(xn(),d.push(p))}else d.push(p)}const l=a.spans.length-d.length;l&&t.recordDroppedEvent("before_send","span",l),a.spans=d}}if(i){if(a.spans){const u=a.spans.length;a.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:u}}return i(a,r)}}return a}function Hn(t){return t.type===void 0}function Ii(t){return t.type==="transaction"}function qc(t){return[{type:"log",item_count:t.length,content_type:"application/vnd.sentry.items.log+json"},{items:t}]}function Gc(t,e,n,r){const s={};return e!=null&&e.sdk&&(s.sdk={name:e.sdk.name,version:e.sdk.version}),n&&r&&(s.dsn=Ee(r)),Jt(s,[qc(t)])}function En(t,e){const n=Wc(t)??[];if(n.length===0)return;const r=t.getOptions(),s=Gc(n,r._metadata,r.tunnel,t.getDsn());Ri().set(t,[]),t.emit("flushLogs"),t.sendEnvelope(s)}function Wc(t){return Ri().get(t)}function Ri(){return pe("clientToLogBufferMap",()=>new WeakMap)}function zc(t,e){e.debug===!0&&(g?m.enable():zt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),v().update(e.initialScope);const r=new t(e);return Vc(r),r.init(),r}function Vc(t){v().setClient(t)}const wi=Symbol.for("SentryBufferFullError");function Yc(t){const e=[];function n(){return t===void 0||e.length<t}function r(o){return e.splice(e.indexOf(o),1)[0]||Promise.resolve(void 0)}function s(o){if(!n())return Xe(wi);const c=o();return e.indexOf(c)===-1&&e.push(c),c.then(()=>r(c)).then(null,()=>r(c).then(null,()=>{})),c}function i(o){return new mt((c,a)=>{let u=e.length;if(!u)return c(!0);const d=setTimeout(()=>{o&&o>0&&c(!1)},o);e.forEach(f=>{wt(f).then(()=>{--u||(clearTimeout(d),c(!0))},a)})})}return{$:e,add:s,drain:i}}const Xc=60*1e3;function Jc(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${t}`);return isNaN(r)?Xc:r-e}function Kc(t,e){return t[e]||t.all||0}function Zc(t,e,n=Date.now()){return Kc(t,e)>n}function Qc(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},i=n==null?void 0:n["x-sentry-rate-limits"],o=n==null?void 0:n["retry-after"];if(i)for(const c of i.trim().split(",")){const[a,u,,,d]=c.split(":",5),f=parseInt(a,10),l=(isNaN(f)?60:f)*1e3;if(!u)s.all=r+l;else for(const p of u.split(";"))p==="metric_bucket"?(!d||d.split(";").includes("custom"))&&(s[p]=r+l):s[p]=r+l}else o?s.all=r+Jc(o,r):e===429&&(s.all=r+60*1e3);return s}const tu=64;function eu(t,e,n=Yc(t.bufferSize||tu)){let r={};const s=o=>n.drain(o);function i(o){const c=[];if(qr(o,(f,l)=>{const p=Gr(l);Zc(r,p)?t.recordDroppedEvent("ratelimit_backoff",p):c.push(f)}),c.length===0)return wt({});const a=Jt(o[0],c),u=f=>{qr(a,(l,p)=>{t.recordDroppedEvent(f,Gr(p))})},d=()=>e({body:Ha(a)}).then(f=>(f.statusCode!==void 0&&(f.statusCode<200||f.statusCode>=300)&&g&&m.warn(`Sentry responded with status code ${f.statusCode} to sent event.`),r=Qc(r,f),f),f=>{throw u("network_error"),g&&m.error("Encountered error running transport request:",f),f});return n.add(d).then(f=>f,f=>{if(f===wi)return g&&m.error("Skipped sending event because buffer is full."),u("queue_overflow"),wt({});throw f})}return{send:i,flush:s}}function nu(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function Ai(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(i=>({name:`${r}:@sentry/${i}`,version:bt})),version:bt}),t._metadata=s}function ki(t={}){const e=t.client||I();if(!kc()||!e)return{};const n=kt(),r=Yt(n);if(r.getTraceData)return r.getTraceData(t);const s=t.scope||v(),i=t.span||j(),o=i?ka(i):ru(s),c=i?lt(i):ui(e,s),a=la(c);return ei.test(o)?{"sentry-trace":o,baggage:a}:(m.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}function ru(t){const{traceId:e,sampled:n,propagationSpanId:r}=t.getPropagationContext();return ni(e,r,n)}const su=100;function At(t,e){const n=I(),r=Ct();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:i=su}=n.getOptions();if(i<=0)return;const c={timestamp:Nt(),...t},a=s?zt(()=>s(c,e)):c;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,i))}let cs;const iu="FunctionToString",us=new WeakMap,ou=()=>({name:iu,setupOnce(){cs=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=ir(this),n=us.has(I())&&e!==void 0?e:this;return cs.apply(n,t)}}catch{}},setup(t){us.set(t,!0)}}),au=ou,cu=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],uu="EventFilters",du=(t={})=>{let e;return{name:uu,setup(n){const r=n.getOptions();e=ds(t,r)},processEvent(n,r,s){if(!e){const i=s.getOptions();e=ds(t,i)}return lu(n,e)?null:n}}},fu=(t={})=>({...du(t),name:"InboundFilters"});function ds(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:cu],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function lu(t,e){if(t.type){if(t.type==="transaction"&&mu(t,e.ignoreTransactions))return g&&m.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${yt(t)}`),!0}else{if(pu(t,e.ignoreErrors))return g&&m.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${yt(t)}`),!0;if(Su(t))return g&&m.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${yt(t)}`),!0;if(hu(t,e.denyUrls))return g&&m.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${yt(t)}.
Url: ${Je(t)}`),!0;if(!gu(t,e.allowUrls))return g&&m.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${yt(t)}.
Url: ${Je(t)}`),!0}return!1}function pu(t,e){return e!=null&&e.length?bi(t).some(n=>ut(n,e)):!1}function mu(t,e){if(!(e!=null&&e.length))return!1;const n=t.transaction;return n?ut(n,e):!1}function hu(t,e){if(!(e!=null&&e.length))return!1;const n=Je(t);return n?ut(n,e):!1}function gu(t,e){if(!(e!=null&&e.length))return!0;const n=Je(t);return n?ut(n,e):!0}function _u(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Je(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(i=>{var o,c,a;return((o=i.mechanism)==null?void 0:o.parent_id)===void 0&&((a=(c=i.stacktrace)==null?void 0:c.frames)==null?void 0:a.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?_u(s):null}catch{return g&&m.error(`Cannot extract url for event ${yt(t)}`),null}}function Su(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)!=null&&n.length?!t.message&&!t.exception.values.some(r=>r.stacktrace||r.type&&r.type!=="Error"||r.value):!1}function yu(t,e,n,r,s,i){var c;if(!((c=s.exception)!=null&&c.values)||!i||!et(i.originalException,Error))return;const o=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;o&&(s.exception.values=Bn(t,e,r,i.originalException,n,s.exception.values,o,0))}function Bn(t,e,n,r,s,i,o,c){if(i.length>=n+1)return i;let a=[...i];if(et(r[s],Error)){fs(o,c);const u=t(e,r[s]),d=a.length;ls(u,s,d,c),a=Bn(t,e,n,r[s],s,[u,...a],u,d)}return Array.isArray(r.errors)&&r.errors.forEach((u,d)=>{if(et(u,Error)){fs(o,c);const f=t(e,u),l=a.length;ls(f,`errors[${d}]`,l,c),a=Bn(t,e,n,u,s,[f,...a],f,l)}}),a}function fs(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function ls(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Eu(t){const e="console";gt(e,t),_t(e,bu)}function bu(){"console"in T&&wo.forEach(function(t){t in T.console&&H(T.console,t,function(e){return Ge[t]=e,function(...n){q("console",{args:n,level:t});const s=Ge[t];s==null||s.apply(T.console,n)}})})}function Tu(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const vu="Dedupe",Iu=()=>{let t;return{name:vu,processEvent(e){if(e.type)return e;try{if(wu(e,t))return g&&m.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}},Ru=Iu;function wu(t,e){return e?!!(Au(t,e)||ku(t,e)):!1}function Au(t,e){const n=t.message,r=e.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!Ci(t,e)||!Ni(t,e))}function ku(t,e){const n=ps(e),r=ps(t);return!(!n||!r||n.type!==r.type||n.value!==r.value||!Ci(t,e)||!Ni(t,e))}function Ni(t,e){let n=Nr(t),r=Nr(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let s=0;s<r.length;s++){const i=r[s],o=n[s];if(i.filename!==o.filename||i.lineno!==o.lineno||i.colno!==o.colno||i.function!==o.function)return!1}return!0}function Ci(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function ps(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}const Nu="thismessage:/";function Pi(t){return"isRelative"in t}function Oi(t,e){const n=t.indexOf("://")<=0&&t.indexOf("//")!==0,r=n?Nu:void 0;try{if("canParse"in URL&&!URL.canParse(t,r))return;const s=new URL(t,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function Cu(t){if(Pi(t))return t.pathname;const e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}function It(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function Pu(t){return t.split(/[?#]/,1)[0]}function Ou(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const{method:i,url:o}=t.fetchData,c=ft()&&e(o);if(t.endTimestamp&&c){const f=t.fetchData.__span;if(!f)return;const l=r[f];l&&(xu(l,t),delete r[f]);return}const a=!!j(),u=c&&a?Kt(Mu(o,i,s)):new pt;if(t.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(t.fetchData.url)){const f=t.args[0],l=t.args[1]||{},p=Lu(f,l,ft()&&a?u:void 0);p&&(t.args[1]=l,l.headers=p)}const d=I();if(d){const f={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};d.emit("beforeOutgoingRequestSpan",u,f)}return u}function Lu(t,e,n){const r=ki({span:n}),s=r["sentry-trace"],i=r.baggage;if(!s)return;const o=e.headers||(js(t)?t.headers:void 0);if(o)if($u(o)){const c=new Headers(o);if(c.get("sentry-trace")||c.set("sentry-trace",s),i){const a=c.get("baggage");a?Ce(a)||c.set("baggage",`${a},${i}`):c.set("baggage",i)}return c}else if(Array.isArray(o)){const c=[...o];o.find(u=>u[0]==="sentry-trace")||c.push(["sentry-trace",s]);const a=o.find(u=>u[0]==="baggage"&&Ce(u[1]));return i&&!a&&c.push(["baggage",i]),c}else{const c="sentry-trace"in o?o["sentry-trace"]:void 0,a="baggage"in o?o.baggage:void 0,u=a?Array.isArray(a)?[...a]:[a]:[],d=a&&(Array.isArray(a)?a.find(f=>Ce(f)):Ce(a));return i&&!d&&u.push(i),{...o,"sentry-trace":c??s,baggage:u.length>0?u.join(","):void 0}}else return{...r}}function xu(t,e){var n,r;if(e.response){Js(t,e.response.status);const s=(r=(n=e.response)==null?void 0:n.headers)==null?void 0:r.get("content-length");if(s){const i=parseInt(s);i>0&&t.setAttribute("http.response_content_length",i)}}else e.error&&t.setStatus({code:O,message:"internal_error"});t.end()}function Ce(t){return t.split(",").some(e=>e.trim().startsWith(cr))}function $u(t){return typeof Headers<"u"&&et(t,Headers)}function Mu(t,e,n){const r=Oi(t);return{name:r?`${e} ${Cu(r)}`:e,attributes:Du(t,r,e,n)}}function Du(t,e,n,r){const s={url:t,type:"fetch","http.method":n,[L]:r,[rt]:"http.client"};return e&&(Pi(e)||(s["http.url"]=e.href,s["server.address"]=e.host),e.search&&(s["http.query"]=e.search),e.hash&&(s["http.fragment"]=e.hash)),s}function Li(t){if(t!==void 0)return t>=400&&t<500?"warning":t>=500?"error":void 0}const fe=T;function Fu(){return"history"in fe&&!!fe.history}function Hu(){if(!("fetch"in fe))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Un(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Bu(){var n;if(typeof EdgeRuntime=="string")return!0;if(!Hu())return!1;if(Un(fe.fetch))return!0;let t=!1;const e=fe.document;if(e&&typeof e.createElement=="function")try{const r=e.createElement("iframe");r.hidden=!0,e.head.appendChild(r),(n=r.contentWindow)!=null&&n.fetch&&(t=Un(r.contentWindow.fetch)),e.head.removeChild(r)}catch(r){g&&m.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",r)}return t}function xi(t,e){const n="fetch";gt(n,t),_t(n,()=>$i(void 0,e))}function Uu(t){const e="fetch-body-resolved";gt(e,t),_t(e,()=>$i(qu))}function $i(t,e=!1){e&&!Bu()||H(T,"fetch",function(n){return function(...r){const s=new Error,{method:i,url:o}=Gu(r),c={args:r,fetchData:{method:i,url:o},startTimestamp:$()*1e3,virtualError:s,headers:Wu(r)};return t||q("fetch",{...c}),n.apply(T,r).then(async a=>(t?t(a):q("fetch",{...c,endTimestamp:$()*1e3,response:a}),a),a=>{if(q("fetch",{...c,endTimestamp:$()*1e3,error:a}),nr(a)&&a.stack===void 0&&(a.stack=s.stack,B(a,"framesToPop",1)),a instanceof TypeError&&(a.message==="Failed to fetch"||a.message==="Load failed"||a.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(c.fetchData.url);a.message=`${a.message} (${u.host})`}catch{}throw a})}})}async function ju(t,e){if(t!=null&&t.body){const n=t.body,r=n.getReader(),s=setTimeout(()=>{n.cancel().then(null,()=>{})},90*1e3);let i=!0;for(;i;){let o;try{o=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);const{done:c}=await r.read();clearTimeout(o),c&&(e(),i=!1)}catch{i=!1}finally{clearTimeout(o)}}clearTimeout(s),r.releaseLock(),n.cancel().then(null,()=>{})}}function qu(t){let e;try{e=t.clone()}catch{return}ju(e,()=>{q("fetch-body-resolved",{endTimestamp:$()*1e3,response:t})})}function jn(t,e){return!!t&&typeof t=="object"&&!!t[e]}function ms(t){return typeof t=="string"?t:t?jn(t,"url")?t.url:t.toString?t.toString():"":""}function Gu(t){if(t.length===0)return{method:"GET",url:""};if(t.length===2){const[n,r]=t;return{url:ms(n),method:jn(r,"method")?String(r.method).toUpperCase():"GET"}}const e=t[0];return{url:ms(e),method:jn(e,"method")?String(e.method).toUpperCase():"GET"}}function Wu(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(js(e))return new Headers(e.headers)}catch{}}function zu(){return"npm"}const y=T;let qn=0;function Mi(){return qn>0}function Vu(){qn++,setTimeout(()=>{qn--})}function Gt(t,e={}){function n(s){return typeof s=="function"}if(!n(t))return t;try{const s=t.__sentry_wrapped__;if(s)return typeof s=="function"?s:t;if(ir(t))return t}catch{return t}const r=function(...s){try{const i=s.map(o=>Gt(o,e));return t.apply(this,i)}catch(i){throw Vu(),sn(o=>{o.addEventProcessor(c=>(e.mechanism&&(Pn(c,void 0),Bt(c,e.mechanism)),c.extra={...c.extra,arguments:s},c)),Ac(i)}),i}};try{for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=t[s])}catch{}Gs(r,t),B(t,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return t.name}})}catch{}return r}function pr(){const t=he(),{referrer:e}=y.document||{},{userAgent:n}=y.navigator||{},r={...e&&{Referer:e},...n&&{"User-Agent":n}};return{url:t,headers:r}}function mr(t,e){const n=hr(t,e),r={type:Zu(e),value:Qu(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Yu(t,e,n,r){const s=I(),i=s==null?void 0:s.getOptions().normalizeDepth,o=sd(e),c={__serialized__:di(e,i)};if(o)return{exception:{values:[mr(t,o)]},extra:c};const a={exception:{values:[{type:rn(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:nd(e,{isUnhandledRejection:r})}]},extra:c};if(n){const u=hr(t,n);u.length&&(a.exception.values[0].stacktrace={frames:u})}return a}function bn(t,e){return{exception:{values:[mr(t,e)]}}}function hr(t,e){const n=e.stacktrace||e.stack||"",r=Ju(e),s=Ku(e);try{return t(n,r,s)}catch{}return[]}const Xu=/Minified React error #\d+;/i;function Ju(t){return t&&Xu.test(t.message)?1:0}function Ku(t){return typeof t.framesToPop=="number"?t.framesToPop:0}function Di(t){return typeof WebAssembly<"u"&&typeof WebAssembly.Exception<"u"?t instanceof WebAssembly.Exception:!1}function Zu(t){const e=t==null?void 0:t.name;return!e&&Di(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function Qu(t){const e=t==null?void 0:t.message;return Di(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function td(t,e,n,r){const s=(n==null?void 0:n.syntheticException)||void 0,i=gr(t,e,s,r);return Bt(i),i.level="error",n!=null&&n.event_id&&(i.event_id=n.event_id),wt(i)}function ed(t,e,n="info",r,s){const i=(r==null?void 0:r.syntheticException)||void 0,o=Gn(t,e,i,s);return o.level=n,r!=null&&r.event_id&&(o.event_id=r.event_id),wt(o)}function gr(t,e,n,r,s){let i;if(Bs(e)&&e.error)return bn(t,e.error);if(Pr(e)||Do(e)){const o=e;if("stack"in e)i=bn(t,e);else{const c=o.name||(Pr(o)?"DOMError":"DOMException"),a=o.message?`${c}: ${o.message}`:c;i=Gn(t,a,n,r),Pn(i,a)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return nr(e)?bn(t,e):ue(e)||rn(e)?(i=Yu(t,e,n,s),Bt(i,{synthetic:!0}),i):(i=Gn(t,e,n,r),Pn(i,`${e}`),Bt(i,{synthetic:!0}),i)}function Gn(t,e,n,r){const s={};if(r&&n){const i=hr(t,n);i.length&&(s.exception={values:[{value:e,stacktrace:{frames:i}}]}),Bt(s,{synthetic:!0})}if(rr(e)){const{__sentry_template_string__:i,__sentry_template_values__:o}=e;return s.logentry={message:i,params:o},s}return s.message=e,s}function nd(t,{isUnhandledRejection:e}){const n=qo(t),r=e?"promise rejection":"exception";return Bs(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:rn(t)?`Event \`${rd(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function rd(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch{}}function sd(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e];if(n instanceof Error)return n}}const id=5e3;class od extends Bc{constructor(e){var c;const n=ad(e),r=y.SENTRY_SDK_SOURCE||zu();Ai(n,"browser",["browser"],r),(c=n._metadata)!=null&&c.sdk&&(n._metadata.sdk.settings={infer_ip:n.sendDefaultPii?"auto":"never",...n._metadata.sdk.settings}),super(n);const{sendDefaultPii:s,sendClientReports:i,enableLogs:o}=this._options;y.document&&(i||o)&&y.document.addEventListener("visibilitychange",()=>{y.document.visibilityState==="hidden"&&(i&&this._flushOutcomes(),o&&En(this))}),o&&(this.on("flush",()=>{En(this)}),this.on("afterCaptureLog",()=>{this._logFlushIdleTimeout&&clearTimeout(this._logFlushIdleTimeout),this._logFlushIdleTimeout=setTimeout(()=>{En(this)},id)})),s&&this.on("beforeSendSession",nu)}eventFromException(e,n){return td(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return ed(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,s)}}function ad(t){var e;return{release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(e=y.SENTRY_RELEASE)==null?void 0:e.id,sendClientReports:!0,parentSpanIsAlwaysRootSpan:!0,...t}}const an=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,S=T,cd=(t,e)=>t>e[1]?"poor":t>e[0]?"needs-improvement":"good",be=(t,e,n,r)=>{let s,i;return o=>{e.value>=0&&(o||r)&&(i=e.value-(s??0),(i||s===void 0)&&(s=e.value,e.delta=i,e.rating=cd(e.value,n),t(e)))}},ud=()=>`v5-${Date.now()}-${Math.floor(Math.random()*(9e12-1))+1e12}`,Te=(t=!0)=>{var n,r;const e=(r=(n=S.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},Zt=()=>{const t=Te();return(t==null?void 0:t.activationStart)??0},ve=(t,e=-1)=>{var i,o;const n=Te();let r="navigate";return n&&((i=S.document)!=null&&i.prerendering||Zt()>0?r="prerender":(o=S.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:e,rating:"good",delta:0,entries:[],id:ud(),navigationType:r}},Tn=new WeakMap;function _r(t,e){return Tn.get(t)||Tn.set(t,new e),Tn.get(t)}class Ke{constructor(){Ke.prototype.__init.call(this),Ke.prototype.__init2.call(this)}__init(){this._sessionValue=0}__init2(){this._sessionEntries=[]}_processEntry(e){var s;if(e.hadRecentInput)return;const n=this._sessionEntries[0],r=this._sessionEntries[this._sessionEntries.length-1];this._sessionValue&&n&&r&&e.startTime-r.startTime<1e3&&e.startTime-n.startTime<5e3?(this._sessionValue+=e.value,this._sessionEntries.push(e)):(this._sessionValue=e.value,this._sessionEntries=[e]),(s=this._onAfterProcessingUnexpectedShift)==null||s.call(this,e)}}const Qt=(t,e,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{e(s.getEntries())})});return r.observe({type:t,buffered:!0,...n}),r}}catch{}},Sr=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let ie=-1;const dd=()=>{var t,e;return((t=S.document)==null?void 0:t.visibilityState)==="hidden"&&!((e=S.document)!=null&&e.prerendering)?0:1/0},Ze=t=>{S.document.visibilityState==="hidden"&&ie>-1&&(ie=t.type==="visibilitychange"?t.timeStamp:0,ld())},fd=()=>{addEventListener("visibilitychange",Ze,!0),addEventListener("prerenderingchange",Ze,!0)},ld=()=>{removeEventListener("visibilitychange",Ze,!0),removeEventListener("prerenderingchange",Ze,!0)},yr=()=>{var t;if(S.document&&ie<0){const e=Zt();ie=(S.document.prerendering||(t=globalThis.performance.getEntriesByType("visibility-state").filter(r=>r.name==="hidden"&&r.startTime>e)[0])==null?void 0:t.startTime)??dd(),fd()}return{get firstHiddenTime(){return ie}}},cn=t=>{var e;(e=S.document)!=null&&e.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},pd=[1800,3e3],md=(t,e={})=>{cn(()=>{const n=yr(),r=ve("FCP");let s;const o=Qt("paint",c=>{for(const a of c)a.name==="first-contentful-paint"&&(o.disconnect(),a.startTime<n.firstHiddenTime&&(r.value=Math.max(a.startTime-Zt(),0),r.entries.push(a),s(!0)))});o&&(s=be(t,r,pd,e.reportAllChanges))})},hd=[.1,.25],gd=(t,e={})=>{md(Sr(()=>{var c,a;const n=ve("CLS",0);let r;const s=_r(e,Ke),i=u=>{for(const d of u)s._processEntry(d);s._sessionValue>n.value&&(n.value=s._sessionValue,n.entries=s._sessionEntries,r())},o=Qt("layout-shift",i);o&&(r=be(t,n,hd,e.reportAllChanges),(c=S.document)==null||c.addEventListener("visibilitychange",()=>{var u;((u=S.document)==null?void 0:u.visibilityState)==="hidden"&&(i(o.takeRecords()),r(!0))}),(a=S==null?void 0:S.setTimeout)==null||a.call(S,r))}))};let Fi=0,vn=1/0,Pe=0;const _d=t=>{t.forEach(e=>{e.interactionId&&(vn=Math.min(vn,e.interactionId),Pe=Math.max(Pe,e.interactionId),Fi=Pe?(Pe-vn)/7+1:0)})};let Wn;const Hi=()=>Wn?Fi:performance.interactionCount||0,Sd=()=>{"interactionCount"in performance||Wn||(Wn=Qt("event",_d,{type:"event",buffered:!0,durationThreshold:0}))},In=10;let Bi=0;const yd=()=>Hi()-Bi;class Qe{constructor(){Qe.prototype.__init.call(this),Qe.prototype.__init2.call(this)}__init(){this._longestInteractionList=[]}__init2(){this._longestInteractionMap=new Map}_resetInteractions(){Bi=Hi(),this._longestInteractionList.length=0,this._longestInteractionMap.clear()}_estimateP98LongestInteraction(){const e=Math.min(this._longestInteractionList.length-1,Math.floor(yd()/50));return this._longestInteractionList[e]}_processEntry(e){var s,i;if((s=this._onBeforeProcessingEntry)==null||s.call(this,e),!(e.interactionId||e.entryType==="first-input"))return;const n=this._longestInteractionList.at(-1);let r=this._longestInteractionMap.get(e.interactionId);if(r||this._longestInteractionList.length<In||e.duration>n._latency){if(r?e.duration>r._latency?(r.entries=[e],r._latency=e.duration):e.duration===r._latency&&e.startTime===r.entries[0].startTime&&r.entries.push(e):(r={id:e.interactionId,entries:[e],_latency:e.duration},this._longestInteractionMap.set(r.id,r),this._longestInteractionList.push(r)),this._longestInteractionList.sort((o,c)=>c._latency-o._latency),this._longestInteractionList.length>In){const o=this._longestInteractionList.splice(In);for(const c of o)this._longestInteractionMap.delete(c.id)}(i=this._onAfterProcessingINPCandidate)==null||i.call(this,r)}}}const Er=t=>{const e=n=>{var r;(n.type==="pagehide"||((r=S.document)==null?void 0:r.visibilityState)==="hidden")&&t(n)};S.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},Ui=t=>{var n;const e=S.requestIdleCallback||S.setTimeout;((n=S.document)==null?void 0:n.visibilityState)==="hidden"?t():(t=Sr(t),e(t),Er(t))},Ed=[200,500],bd=40,Td=(t,e={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&cn(()=>{Sd();const n=ve("INP");let r;const s=_r(e,Qe),i=c=>{Ui(()=>{for(const u of c)s._processEntry(u);const a=s._estimateP98LongestInteraction();a&&a._latency!==n.value&&(n.value=a._latency,n.entries=a.entries,r())})},o=Qt("event",i,{durationThreshold:e.durationThreshold??bd});r=be(t,n,Ed,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),Er(()=>{i(o.takeRecords()),r(!0)}))})};class vd{_processEntry(e){var n;(n=this._onBeforeProcessingEntry)==null||n.call(this,e)}}const Id=[2500,4e3],Rd=(t,e={})=>{cn(()=>{const n=yr(),r=ve("LCP");let s;const i=_r(e,vd),o=a=>{e.reportAllChanges||(a=a.slice(-1));for(const u of a)i._processEntry(u),u.startTime<n.firstHiddenTime&&(r.value=Math.max(u.startTime-Zt(),0),r.entries=[u],s())},c=Qt("largest-contentful-paint",o);if(c){s=be(t,r,Id,e.reportAllChanges);const a=Sr(()=>{o(c.takeRecords()),c.disconnect(),s(!0)});for(const u of["keydown","click","visibilitychange"])S.document&&addEventListener(u,()=>Ui(a),{capture:!0,once:!0})}})},wd=[800,1800],zn=t=>{var e,n;(e=S.document)!=null&&e.prerendering?cn(()=>zn(t)):((n=S.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>zn(t),!0):setTimeout(t)},Ad=(t,e={})=>{const n=ve("TTFB"),r=be(t,n,wd,e.reportAllChanges);zn(()=>{const s=Te();s&&(n.value=Math.max(s.responseStart-Zt(),0),n.entries=[s],r(!0))})},oe={},tn={};let ji,qi,Gi,Wi;function zi(t,e=!1){return un("cls",t,Cd,ji,e)}function Vi(t,e=!1){return un("lcp",t,Pd,qi,e)}function kd(t){return un("ttfb",t,Od,Gi)}function Nd(t){return un("inp",t,Ld,Wi)}function Wt(t,e){return Yi(t,e),tn[t]||(xd(t),tn[t]=!0),Xi(t,e)}function Ie(t,e){const n=oe[t];if(n!=null&&n.length)for(const r of n)try{r(e)}catch(s){an&&m.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${tt(r)}
Error:`,s)}}function Cd(){return gd(t=>{Ie("cls",{metric:t}),ji=t},{reportAllChanges:!0})}function Pd(){return Rd(t=>{Ie("lcp",{metric:t}),qi=t},{reportAllChanges:!0})}function Od(){return Ad(t=>{Ie("ttfb",{metric:t}),Gi=t})}function Ld(){return Td(t=>{Ie("inp",{metric:t}),Wi=t})}function un(t,e,n,r,s=!1){Yi(t,e);let i;return tn[t]||(i=n(),tn[t]=!0),r&&e({metric:r}),Xi(t,e,s?i:void 0)}function xd(t){const e={};t==="event"&&(e.durationThreshold=0),Qt(t,n=>{Ie(t,{entries:n})},e)}function Yi(t,e){oe[t]=oe[t]||[],oe[t].push(e)}function Xi(t,e,n){return()=>{n&&n();const r=oe[t];if(!r)return;const s=r.indexOf(e);s!==-1&&r.splice(s,1)}}function $d(t){return"duration"in t}function Rn(t){return typeof t=="number"&&isFinite(t)}function ht(t,e,n,{...r}){const s=b(t).start_timestamp;return s&&s>e&&typeof t.updateStartTime=="function"&&t.updateStartTime(e),fr(t,()=>{const i=Kt({startTime:e,...r});return i&&i.end(n),i})}function br(t){var A;const e=I();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:i}=t,{release:o,environment:c,sendDefaultPii:a}=e.getOptions(),u=e.getIntegrationByName("Replay"),d=u==null?void 0:u.getReplayId(),f=v(),l=f.getUser(),p=l!==void 0?l.email||l.id||l.ip_address:void 0;let h;try{h=f.getScopeData().contexts.profile.profile_id}catch{}const _={release:o,environment:c,user:p||void 0,profile_id:h||void 0,replay_id:d||void 0,transaction:r,"user_agent.original":(A=S.navigator)==null?void 0:A.userAgent,"client.address":a?"{{auto}}":void 0,...s};return Kt({name:n,attributes:_,startTime:i,experimental:{standalone:!0}})}function dn(){return S.addEventListener&&S.performance}function k(t){return t/1e3}function Ji(t){let e="unknown",n="unknown",r="";for(const s of t){if(s==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=s}return r===t&&(e=r),{name:e,version:n}}function Ki(t){try{return PerformanceObserver.supportedEntryTypes.includes(t)}catch{return!1}}function Zi(t,e){let n,r=!1;function s(c){!r&&n&&e(c,n),r=!0}Er(()=>{s("pagehide")});const i=t.on("beforeStartNavigationSpan",(c,a)=>{a!=null&&a.isRedirect||(s("navigation"),hs(i,o))}),o=t.on("afterStartPageLoadSpan",c=>{n=c.spanContext().spanId,hs(o)})}function hs(...t){t.forEach(e=>e&&setTimeout(e,0))}function Md(t){let e=0,n;if(!Ki("layout-shift"))return;const r=zi(({metric:s})=>{const i=s.entries[s.entries.length-1];i&&(e=s.value,n=i)},!0);Zi(t,(s,i)=>{Dd(e,n,i,s),r()})}function Dd(t,e,n,r){var u;an&&m.log(`Sending CLS span (${t})`);const s=k((U()||0)+((e==null?void 0:e.startTime)||0)),i=v().getScopeData().transactionName,o=e?K((u=e.sources[0])==null?void 0:u.node):"Layout shift",c={[L]:"auto.http.browser.cls",[rt]:"ui.webvital.cls",[Xt]:(e==null?void 0:e.duration)||0,"sentry.pageload.span_id":n,"sentry.report_event":r};e!=null&&e.sources&&e.sources.forEach((d,f)=>{c[`cls.source.${f+1}`]=K(d.node)});const a=br({name:o,transaction:i,attributes:c,startTime:s});a&&(a.addEvent("cls",{[Se]:"",[ye]:t}),a.end(s))}function Fd(t){let e=0,n;if(!Ki("largest-contentful-paint"))return;const r=Vi(({metric:s})=>{const i=s.entries[s.entries.length-1];i&&(e=s.value,n=i)},!0);Zi(t,(s,i)=>{Hd(e,n,i,s),r()})}function Hd(t,e,n,r){an&&m.log(`Sending LCP span (${t})`);const s=k((U()||0)+((e==null?void 0:e.startTime)||0)),i=v().getScopeData().transactionName,o=e?K(e.element):"Largest contentful paint",c={[L]:"auto.http.browser.lcp",[rt]:"ui.webvital.lcp",[Xt]:0,"sentry.pageload.span_id":n,"sentry.report_event":r};e&&(e.element&&(c["lcp.element"]=K(e.element)),e.id&&(c["lcp.id"]=e.id),e.url&&(c["lcp.url"]=e.url.trim().slice(0,200)),e.loadTime!=null&&(c["lcp.loadTime"]=e.loadTime),e.renderTime!=null&&(c["lcp.renderTime"]=e.renderTime),e.size!=null&&(c["lcp.size"]=e.size));const a=br({name:o,transaction:i,attributes:c,startTime:s});a&&(a.addEvent("lcp",{[Se]:"millisecond",[ye]:t}),a.end(s))}const Bd=2147483647;let gs=0,V={},F,Dt;function Ud({recordClsStandaloneSpans:t,recordLcpStandaloneSpans:e,client:n}){const r=dn();if(r&&U()){r.mark&&S.performance.mark("sentry-tracing-init");const s=e?Fd(n):zd(),i=Vd(),o=t?Md(n):Wd();return()=>{s==null||s(),i(),o==null||o()}}return()=>{}}function jd(){Wt("longtask",({entries:t})=>{const e=j();if(!e)return;const{op:n,start_timestamp:r}=b(e);for(const s of t){const i=k(U()+s.startTime),o=k(s.duration);n==="navigation"&&r&&i<r||ht(e,i,i+o,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[L]:"auto.ui.browser.metrics"}})}})}function qd(){new PerformanceObserver(e=>{const n=j();if(n)for(const r of e.getEntries()){if(!r.scripts[0])continue;const s=k(U()+r.startTime),{start_timestamp:i,op:o}=b(n);if(o==="navigation"&&i&&s<i)continue;const c=k(r.duration),a={[L]:"auto.ui.browser.metrics"},u=r.scripts[0],{invoker:d,invokerType:f,sourceURL:l,sourceFunctionName:p,sourceCharPosition:h}=u;a["browser.script.invoker"]=d,a["browser.script.invoker_type"]=f,l&&(a["code.filepath"]=l),p&&(a["code.function"]=p),h!==-1&&(a["browser.script.source_char_position"]=h),ht(n,s,s+c,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:a})}}).observe({type:"long-animation-frame",buffered:!0})}function Gd(){Wt("event",({entries:t})=>{const e=j();if(e){for(const n of t)if(n.name==="click"){const r=k(U()+n.startTime),s=k(n.duration),i={name:K(n.target),op:`ui.interaction.${n.name}`,startTime:r,attributes:{[L]:"auto.ui.browser.metrics"}},o=qs(n.target);o&&(i.attributes["ui.component_name"]=o),ht(e,r,r+s,i)}}})}function Wd(){return zi(({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(V.cls={value:t.value,unit:""},Dt=e)},!0)}function zd(){return Vi(({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(V.lcp={value:t.value,unit:"millisecond"},F=e)},!0)}function Vd(){return kd(({metric:t})=>{t.entries[t.entries.length-1]&&(V.ttfb={value:t.value,unit:"millisecond"})})}function Yd(t,e){const n=dn(),r=U();if(!(n!=null&&n.getEntries)||!r)return;const s=k(r),i=n.getEntries(),{op:o,start_timestamp:c}=b(t);i.slice(gs).forEach(a=>{const u=k(a.startTime),d=k(Math.max(0,a.duration));if(!(o==="navigation"&&c&&s+u<c))switch(a.entryType){case"navigation":{Kd(t,a,s);break}case"mark":case"paint":case"measure":{Xd(t,a,u,d,s,e.ignorePerformanceApiSpans);const f=yr(),l=a.startTime<f.firstHiddenTime;a.name==="first-paint"&&l&&(V.fp={value:a.startTime,unit:"millisecond"}),a.name==="first-contentful-paint"&&l&&(V.fcp={value:a.startTime,unit:"millisecond"});break}case"resource":{tf(t,a,a.name,u,d,s,e.ignoreResourceSpans);break}}}),gs=Math.max(i.length-1,0),ef(t),o==="pageload"&&(rf(V),e.recordClsOnPageloadSpan||delete V.cls,e.recordLcpOnPageloadSpan||delete V.lcp,Object.entries(V).forEach(([a,u])=>{Qa(a,u.value,u.unit)}),t.setAttribute("performance.timeOrigin",s),t.setAttribute("performance.activationStart",Zt()),nf(t,e)),F=void 0,Dt=void 0,V={}}function Xd(t,e,n,r,s,i){if(["mark","measure"].includes(e.entryType)&&ut(e.name,i))return;const o=Te(!1),c=k(o?o.requestStart:0),a=s+Math.max(n,c),u=s+n,d=u+r,f={[L]:"auto.resource.browser.metrics"};a!==u&&(f["sentry.browser.measure_happened_before_request"]=!0,f["sentry.browser.measure_start_time"]=a),Jd(f,e),a<=d&&ht(t,a,d,{name:e.name,op:e.entryType,attributes:f})}function Jd(t,e){try{const n=e.detail;if(!n)return;if(typeof n=="object"){for(const[r,s]of Object.entries(n))if(s&&ce(s))t[`sentry.browser.measure.detail.${r}`]=s;else if(s!==void 0)try{t[`sentry.browser.measure.detail.${r}`]=JSON.stringify(s)}catch{}return}if(ce(n)){t["sentry.browser.measure.detail"]=n;return}try{t["sentry.browser.measure.detail"]=JSON.stringify(n)}catch{}}catch{}}function Kd(t,e,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(r=>{Oe(t,e,r,n)}),Oe(t,e,"secureConnection",n,"TLS/SSL"),Oe(t,e,"fetch",n,"cache"),Oe(t,e,"domainLookup",n,"DNS"),Qd(t,e,n)}function Oe(t,e,n,r,s=n){const i=Zd(n),o=e[i],c=e[`${n}Start`];!c||!o||ht(t,r+k(c),r+k(o),{op:`browser.${s}`,name:e.name,attributes:{[L]:"auto.ui.browser.metrics",...n==="redirect"&&e.redirectCount!=null?{"http.redirect_count":e.redirectCount}:{}}})}function Zd(t){return t==="secureConnection"?"connectEnd":t==="fetch"?"domainLookupStart":`${t}End`}function Qd(t,e,n){const r=n+k(e.requestStart),s=n+k(e.responseEnd),i=n+k(e.responseStart);e.responseEnd&&(ht(t,r,s,{op:"browser.request",name:e.name,attributes:{[L]:"auto.ui.browser.metrics"}}),ht(t,i,s,{op:"browser.response",name:e.name,attributes:{[L]:"auto.ui.browser.metrics"}}))}function tf(t,e,n,r,s,i,o){if(e.initiatorType==="xmlhttprequest"||e.initiatorType==="fetch")return;const c=e.initiatorType?`resource.${e.initiatorType}`:"resource.other";if(o!=null&&o.includes(c))return;const a=It(n),u={[L]:"auto.resource.browser.metrics"};wn(u,e,"transferSize","http.response_transfer_size"),wn(u,e,"encodedBodySize","http.response_content_length"),wn(u,e,"decodedBodySize","http.decoded_response_content_length");const d=e.deliveryType;d!=null&&(u["http.response_delivery_type"]=d);const f=e.renderBlockingStatus;if(f&&(u["resource.render_blocking_status"]=f),a.protocol&&(u["url.scheme"]=a.protocol.split(":").pop()),a.host&&(u["server.address"]=a.host),u["url.same_origin"]=n.includes(S.location.origin),e.nextHopProtocol!=null){const{name:h,version:_}=Ji(e.nextHopProtocol);u["network.protocol.name"]=h,u["network.protocol.version"]=_}const l=i+r,p=l+s;ht(t,l,p,{name:n.replace(S.location.origin,""),op:c,attributes:u})}function ef(t){const e=S.navigator;if(!e)return;const n=e.connection;n&&(n.effectiveType&&t.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&t.setAttribute("connectionType",n.type),Rn(n.rtt)&&(V["connection.rtt"]={value:n.rtt,unit:"millisecond"})),Rn(e.deviceMemory)&&t.setAttribute("deviceMemory",`${e.deviceMemory} GB`),Rn(e.hardwareConcurrency)&&t.setAttribute("hardwareConcurrency",String(e.hardwareConcurrency))}function nf(t,e){F&&e.recordLcpOnPageloadSpan&&(F.element&&t.setAttribute("lcp.element",K(F.element)),F.id&&t.setAttribute("lcp.id",F.id),F.url&&t.setAttribute("lcp.url",F.url.trim().slice(0,200)),F.loadTime!=null&&t.setAttribute("lcp.loadTime",F.loadTime),F.renderTime!=null&&t.setAttribute("lcp.renderTime",F.renderTime),t.setAttribute("lcp.size",F.size)),Dt!=null&&Dt.sources&&e.recordClsOnPageloadSpan&&Dt.sources.forEach((n,r)=>t.setAttribute(`cls.source.${r+1}`,K(n.node)))}function wn(t,e,n,r){const s=e[n];s!=null&&s<Bd&&(t[r]=s)}function rf(t){const e=Te(!1);if(!e)return;const{responseStart:n,requestStart:r}=e;r<=n&&(t["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}function sf(){return dn()&&U()?Wt("element",of):()=>{}}const of=({entries:t})=>{const e=j(),n=e?D(e):void 0,r=n?b(n).description:v().getScopeData().transactionName;t.forEach(s=>{var p,h;const i=s;if(!i.identifier)return;const o=i.name,c=i.renderTime,a=i.loadTime,[u,d]=a?[k(a),"load-time"]:c?[k(c),"render-time"]:[$(),"entry-emission"],f=o==="image-paint"?k(Math.max(0,(c??0)-(a??0))):0,l={[L]:"auto.ui.browser.elementtiming",[rt]:"ui.elementtiming",[J]:"component","sentry.span_start_time_source":d,"sentry.transaction_name":r,"element.id":i.id,"element.type":((h=(p=i.element)==null?void 0:p.tagName)==null?void 0:h.toLowerCase())||"unknown","element.size":i.naturalWidth&&i.naturalHeight?`${i.naturalWidth}x${i.naturalHeight}`:void 0,"element.render_time":c,"element.load_time":a,"element.url":i.url||void 0,"element.identifier":i.identifier,"element.paint_type":o};ic({name:`element[${i.identifier}]`,attributes:l,startTime:u,onlyIfParent:!0},_=>{_.end(u+f)})})},af=1e3;let _s,Vn,Yn;function cf(t){const e="dom";gt(e,t),_t(e,uf)}function uf(){if(!S.document)return;const t=q.bind(null,"dom"),e=Ss(t,!0);S.document.addEventListener("click",e,!1),S.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var i,o;const s=(i=S[n])==null?void 0:i.prototype;(o=s==null?void 0:s.hasOwnProperty)!=null&&o.call(s,"addEventListener")&&(H(s,"addEventListener",function(c){return function(a,u,d){if(a==="click"||a=="keypress")try{const f=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},l=f[a]=f[a]||{refCount:0};if(!l.handler){const p=Ss(t);l.handler=p,c.call(this,a,p,d)}l.refCount++}catch{}return c.call(this,a,u,d)}}),H(s,"removeEventListener",function(c){return function(a,u,d){if(a==="click"||a=="keypress")try{const f=this.__sentry_instrumentation_handlers__||{},l=f[a];l&&(l.refCount--,l.refCount<=0&&(c.call(this,a,l.handler,d),l.handler=void 0,delete f[a]),Object.keys(f).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return c.call(this,a,u,d)}}))})}function df(t){if(t.type!==Vn)return!1;try{if(!t.target||t.target._sentryId!==Yn)return!1}catch{}return!0}function ff(t,e){return t!=="keypress"?!1:e!=null&&e.tagName?!(e.tagName==="INPUT"||e.tagName==="TEXTAREA"||e.isContentEditable):!0}function Ss(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=lf(n);if(ff(n.type,r))return;B(n,"_sentryCaptured",!0),r&&!r._sentryId&&B(r,"_sentryId",G());const s=n.type==="keypress"?"input":n.type;df(n)||(t({event:n,name:s,global:e}),Vn=n.type,Yn=r?r._sentryId:void 0),clearTimeout(_s),_s=S.setTimeout(()=>{Yn=void 0,Vn=void 0},af)}}function lf(t){try{return t.target}catch{return null}}let Le;function Tr(t){const e="history";gt(e,t),_t(e,pf)}function pf(){if(S.addEventListener("popstate",()=>{const e=S.location.href,n=Le;if(Le=e,n===e)return;q("history",{from:n,to:e})}),!Fu())return;function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=Le,i=mf(String(r));if(Le=i,s===i)return e.apply(this,n);q("history",{from:s,to:i})}return e.apply(this,n)}}H(S.history,"pushState",t),H(S.history,"replaceState",t)}function mf(t){try{return new URL(t,S.location.origin).toString()}catch{return t}}const je={};function hf(t){const e=je[t];if(e)return e;let n=S[t];if(Un(n))return je[t]=n.bind(S);const r=S.document;if(r&&typeof r.createElement=="function")try{const s=r.createElement("iframe");s.hidden=!0,r.head.appendChild(s);const i=s.contentWindow;i!=null&&i[t]&&(n=i[t]),r.head.removeChild(s)}catch(s){an&&m.warn(`Could not create sandbox iframe for ${t} check, bailing to window.${t}: `,s)}return n&&(je[t]=n.bind(S))}function ys(t){je[t]=void 0}const Ft="__sentry_xhr_v3__";function Qi(t){const e="xhr";gt(e,t),_t(e,gf)}function gf(){if(!S.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=new Error,i=$()*1e3,o=Q(r[0])?r[0].toUpperCase():void 0,c=_f(r[1]);if(!o||!c)return e.apply(n,r);n[Ft]={method:o,url:c,request_headers:{}},o==="POST"&&c.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=()=>{const u=n[Ft];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}const d={endTimestamp:$()*1e3,startTimestamp:i,xhr:n,virtualError:s};q("xhr",d)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(u,d,f){return a(),u.apply(d,f)}}):n.addEventListener("readystatechange",a),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,d,f){const[l,p]=f,h=d[Ft];return h&&Q(l)&&Q(p)&&(h.request_headers[l.toLowerCase()]=p),u.apply(d,f)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[Ft];if(!s)return e.apply(n,r);r[0]!==void 0&&(s.body=r[0]);const i={startTimestamp:$()*1e3,xhr:n};return q("xhr",i),e.apply(n,r)}})}function _f(t){if(Q(t))return t;try{return t.toString()}catch{}}const An=[],qe=new Map,Sf=60;function yf(){if(dn()&&U()){const e=Ef();return()=>{e()}}return()=>{}}const Es={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Ef(){return Nd(bf)}const bf=({metric:t})=>{if(t.value==null)return;const e=k(t.value);if(e>Sf)return;const n=t.entries.find(h=>h.duration===t.value&&Es[h.name]);if(!n)return;const{interactionId:r}=n,s=Es[n.name],i=k(U()+n.startTime),o=j(),c=o?D(o):void 0,u=(r!=null?qe.get(r):void 0)||c,d=u?b(u).description:v().getScopeData().transactionName,f=K(n.target),l={[L]:"auto.http.browser.inp",[rt]:`ui.interaction.${s}`,[Xt]:n.duration},p=br({name:f,transaction:d,attributes:l,startTime:i});p&&(p.addEvent("inp",{[Se]:"millisecond",[ye]:t.value}),p.end(i+e))};function Tf(){const t=({entries:e})=>{const n=j(),r=n&&D(n);e.forEach(s=>{if(!$d(s)||!r)return;const i=s.interactionId;if(i!=null&&!qe.has(i)){if(An.length>10){const o=An.shift();qe.delete(o)}An.push(i),qe.set(i,r)}})};Wt("event",t),Wt("first-input",t)}function vf(t,e=hf("fetch")){let n=0,r=0;function s(i){const o=i.body.length;n+=o,r++;const c={body:i.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return ys("fetch"),Xe("No fetch implementation available");try{return e(t.url,c).then(a=>(n-=o,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return ys("fetch"),n-=o,r--,Xe(a)}}return eu(t,s)}const If=30,Rf=50;function Xn(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?Rt:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const wf=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Af=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,kf=/\((\S*)(?::(\d+))(?::(\d+))\)/,Nf=/at (.+?) ?\(data:(.+?),/,Cf=t=>{const e=t.match(Nf);if(e)return{filename:`<data:${e[2]}>`,function:e[1]};const n=wf.exec(t);if(n){const[,s,i,o]=n;return Xn(s,Rt,+i,+o)}const r=Af.exec(t);if(r){if(r[2]&&r[2].indexOf("eval")===0){const c=kf.exec(r[2]);c&&(r[2]=c[1],r[3]=c[2],r[4]=c[3])}const[i,o]=to(r[1]||Rt,r[2]);return Xn(o,i,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}},Pf=[If,Cf],Of=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Lf=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,xf=t=>{const e=Of.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const i=Lf.exec(e[3]);i&&(e[1]=e[1]||"eval",e[3]=i[1],e[4]=i[2],e[5]="")}let r=e[3],s=e[1]||Rt;return[s,r]=to(s,r),Xn(r,s,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}},$f=[Rf,xf],Mf=[Pf,$f],Df=Ms(...Mf),to=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:Rt,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},W=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,xe=1024,Ff="Breadcrumbs",Hf=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:Ff,setup(n){e.console&&Eu(qf(n)),e.dom&&cf(jf(n,e.dom)),e.xhr&&Qi(Gf(n)),e.fetch&&xi(Wf(n)),e.history&&Tr(zf(n)),e.sentry&&n.on("beforeSendEvent",Uf(n))}}},Bf=Hf;function Uf(t){return function(n){I()===t&&At({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:yt(n)},{event:n})}}function jf(t,e){return function(r){if(I()!==t)return;let s,i,o=typeof e=="object"?e.serializeAttribute:void 0,c=typeof e=="object"&&typeof e.maxStringLength=="number"?e.maxStringLength:void 0;c&&c>xe&&(W&&m.warn(`\`dom.maxStringLength\` cannot exceed ${xe}, but a value of ${c} was configured. Sentry will use ${xe} instead.`),c=xe),typeof o=="string"&&(o=[o]);try{const u=r.event,d=Vf(u)?u.target:u;s=K(d,{keyAttrs:o,maxStringLength:c}),i=qs(d)}catch{s="<unknown>"}if(s.length===0)return;const a={category:`ui.${r.name}`,message:s};i&&(a.data={"ui.component_name":i}),At(a,{event:r.event,name:r.name,global:r.global})}}function qf(t){return function(n){if(I()!==t)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:Tu(n.level),message:Or(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${Or(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;At(r,{input:n.args,level:n.level})}}function Gf(t){return function(n){if(I()!==t)return;const{startTimestamp:r,endTimestamp:s}=n,i=n.xhr[Ft];if(!r||!s||!i)return;const{method:o,url:c,status_code:a,body:u}=i,d={method:o,url:c,status_code:a},f={xhr:n.xhr,input:u,startTimestamp:r,endTimestamp:s},l={category:"xhr",data:d,type:"http",level:Li(a)};t.emit("beforeOutgoingRequestBreadcrumb",l,f),At(l,f)}}function Wf(t){return function(n){if(I()!==t)return;const{startTimestamp:r,endTimestamp:s}=n;if(s&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.fetchData.method,n.fetchData.url,n.error){const i=n.fetchData,o={data:n.error,input:n.args,startTimestamp:r,endTimestamp:s},c={category:"fetch",data:i,level:"error",type:"http"};t.emit("beforeOutgoingRequestBreadcrumb",c,o),At(c,o)}else{const i=n.response,o={...n.fetchData,status_code:i==null?void 0:i.status};n.fetchData.request_body_size,n.fetchData.response_body_size,i==null||i.status;const c={input:n.args,response:i,startTimestamp:r,endTimestamp:s},a={category:"fetch",data:o,type:"http",level:Li(o.status_code)};t.emit("beforeOutgoingRequestBreadcrumb",a,c),At(a,c)}}}function zf(t){return function(n){if(I()!==t)return;let r=n.from,s=n.to;const i=It(y.location.href);let o=r?It(r):void 0;const c=It(s);o!=null&&o.path||(o=i),i.protocol===c.protocol&&i.host===c.host&&(s=c.relative),i.protocol===o.protocol&&i.host===o.host&&(r=o.relative),At({category:"navigation",data:{from:r,to:s}})}}function Vf(t){return!!t&&!!t.target}const Yf=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Xf="BrowserApiErrors",Jf=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,unregisterOriginalCallbacks:!1,...t};return{name:Xf,setupOnce(){e.setTimeout&&H(y,"setTimeout",bs),e.setInterval&&H(y,"setInterval",bs),e.requestAnimationFrame&&H(y,"requestAnimationFrame",Zf),e.XMLHttpRequest&&"XMLHttpRequest"in y&&H(XMLHttpRequest.prototype,"send",Qf);const n=e.eventTarget;n&&(Array.isArray(n)?n:Yf).forEach(s=>tl(s,e))}}},Kf=Jf;function bs(t){return function(...e){const n=e[0];return e[0]=Gt(n,{mechanism:{data:{function:tt(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Zf(t){return function(e){return t.apply(this,[Gt(e,{mechanism:{data:{function:"requestAnimationFrame",handler:tt(t)},handled:!1,type:"instrument"}})])}}function Qf(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(s=>{s in n&&typeof n[s]=="function"&&H(n,s,function(i){const o={mechanism:{data:{function:s,handler:tt(i)},handled:!1,type:"instrument"}},c=ir(i);return c&&(o.mechanism.data.handler=tt(c)),Gt(i,o)})}),t.apply(this,e)}}function tl(t,e){var s,i;const r=(s=y[t])==null?void 0:s.prototype;(i=r==null?void 0:r.hasOwnProperty)!=null&&i.call(r,"addEventListener")&&(H(r,"addEventListener",function(o){return function(c,a,u){try{el(a)&&(a.handleEvent=Gt(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:tt(a),target:t},handled:!1,type:"instrument"}}))}catch{}return e.unregisterOriginalCallbacks&&nl(this,c,a),o.apply(this,[c,Gt(a,{mechanism:{data:{function:"addEventListener",handler:tt(a),target:t},handled:!1,type:"instrument"}}),u])}}),H(r,"removeEventListener",function(o){return function(c,a,u){try{const d=a.__sentry_wrapped__;d&&o.call(this,c,d,u)}catch{}return o.call(this,c,a,u)}}))}function el(t){return typeof t.handleEvent=="function"}function nl(t,e,n){t&&typeof t=="object"&&"removeEventListener"in t&&typeof t.removeEventListener=="function"&&t.removeEventListener(e,n)}const rl=()=>({name:"BrowserSession",setupOnce(){if(typeof y.document>"u"){W&&m.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.");return}ts({ignoreDuration:!0}),es(),Tr(({from:t,to:e})=>{t!==void 0&&t!==e&&(ts({ignoreDuration:!0}),es())})}}),sl="GlobalHandlers",il=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:sl,setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(al(n),Ts("onerror")),e.onunhandledrejection&&(cl(n),Ts("onunhandledrejection"))}}},ol=il;function al(t){Ds(e=>{const{stackParser:n,attachStacktrace:r}=eo();if(I()!==t||Mi())return;const{msg:s,url:i,line:o,column:c,error:a}=e,u=fl(gr(n,a||s,void 0,r,!1),i,o,c);u.level="error",_i(u,{originalException:a,mechanism:{handled:!1,type:"auto.browser.global_handlers.onerror"}})})}function cl(t){Fs(e=>{const{stackParser:n,attachStacktrace:r}=eo();if(I()!==t||Mi())return;const s=ul(e),i=ce(s)?dl(s):gr(n,s,void 0,r,!0);i.level="error",_i(i,{originalException:s,mechanism:{handled:!1,type:"auto.browser.global_handlers.onunhandledrejection"}})})}function ul(t){if(ce(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch{}return t}function dl(t){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}}function fl(t,e,n,r){const s=t.exception=t.exception||{},i=s.values=s.values||[],o=i[0]=i[0]||{},c=o.stacktrace=o.stacktrace||{},a=c.frames=c.frames||[],u=r,d=n,f=ll(e)??he();return a.length===0&&a.push({colno:u,filename:f,function:Rt,in_app:!0,lineno:d}),t}function Ts(t){W&&m.log(`Global Handler attached: ${t}`)}function eo(){const t=I();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}function ll(t){if(!(!Q(t)||t.length===0)){if(t.startsWith("data:")){const e=t.match(/^data:([^;]+)/),n=e?e[1]:"text/javascript",r=t.includes("base64,");return`<data:${n}${r?",base64":""}>`}return t.slice(0,1024)}}const pl=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!y.navigator&&!y.location&&!y.document)return;const e=pr(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),ml="cause",hl=5,gl="LinkedErrors",_l=(t={})=>{const e=t.limit||hl,n=t.key||ml;return{name:gl,preprocessEvent(r,s,i){const o=i.getOptions();yu(mr,o.stackParser,n,e,r,s)}}},Sl=_l;function yl(){return El()?(W&&zt(()=>{console.error("[Sentry] You cannot use Sentry.init() in a browser extension, see: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}),!0):!1}function El(){var i;if(typeof y.window>"u")return!1;const t=y;if(t.nw)return!1;const e=t.chrome||t.browser;if(!((i=e==null?void 0:e.runtime)!=null&&i.id))return!1;const n=he(),r=["chrome-extension","moz-extension","ms-browser-extension","safari-web-extension"];return!(y===y.top&&r.some(o=>n.startsWith(`${o}://`)))}function bl(t){return[fu(),au(),Kf(),Bf(),ol(),Sl(),Ru(),pl(),rl()]}function Tl(t={}){const e=!t.skipBrowserExtensionCheck&&yl(),n={...t,enabled:e?!1:t.enabled,stackParser:Lo(t.stackParser||Df),integrations:$c({integrations:t.integrations,defaultIntegrations:t.defaultIntegrations==null?bl():t.defaultIntegrations}),transport:t.transport||vf};return zc(od,n)}function X(t=0){return((U()||performance.timeOrigin)+t)/1e3}function vl(t){const e=[];if(t.nextHopProtocol!=null){const{name:n,version:r}=Ji(t.nextHopProtocol);e.push(["network.protocol.version",r],["network.protocol.name",n])}return U()?[...e,["http.request.redirect_start",X(t.redirectStart)],["http.request.fetch_start",X(t.fetchStart)],["http.request.domain_lookup_start",X(t.domainLookupStart)],["http.request.domain_lookup_end",X(t.domainLookupEnd)],["http.request.connect_start",X(t.connectStart)],["http.request.secure_connection_start",X(t.secureConnectionStart)],["http.request.connection_end",X(t.connectEnd)],["http.request.request_start",X(t.requestStart)],["http.request.response_start",X(t.responseStart)],["http.request.response_end",X(t.responseEnd)]]:e}const vs=new WeakMap,kn=new Map,no={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function Il(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:i,enableHTTPTimings:o,tracePropagationTargets:c,onRequestSpanStart:a}={...no,...e},u=typeof i=="function"?i:l=>!0,d=l=>wl(l,c),f={};n&&(t.addEventProcessor(l=>(l.type==="transaction"&&l.spans&&l.spans.forEach(p=>{if(p.op==="http.client"){const h=kn.get(p.span_id);h&&(p.timestamp=h/1e3,kn.delete(p.span_id))}}),l)),s&&Uu(l=>{if(l.response){const p=vs.get(l.response);p&&l.endTimestamp&&kn.set(p,l.endTimestamp)}}),xi(l=>{const p=Ou(l,u,d,f);if(l.response&&l.fetchData.__span&&vs.set(l.response,l.fetchData.__span),p){const h=ro(l.fetchData.url),_=h?It(h).host:void 0;p.setAttributes({"http.url":h,"server.address":_}),o&&Is(p),a==null||a(p,{headers:l.headers})}})),r&&Qi(l=>{var h;const p=Al(l,u,d,f);if(p){o&&Is(p);let _;try{_=new Headers((h=l.xhr.__sentry_xhr_v3__)==null?void 0:h.request_headers)}catch{}a==null||a(p,{headers:_})}})}function Rl(t){return t.entryType==="resource"&&"initiatorType"in t&&typeof t.nextHopProtocol=="string"&&(t.initiatorType==="fetch"||t.initiatorType==="xmlhttprequest")}function Is(t){const{url:e}=b(t).data;if(!e||typeof e!="string")return;const n=Wt("resource",({entries:r})=>{r.forEach(s=>{Rl(s)&&s.name.endsWith(e)&&(vl(s).forEach(o=>t.setAttribute(...o)),setTimeout(n))})})}function wl(t,e){const n=he();if(n){let r,s;try{r=new URL(t,n),s=new URL(n).origin}catch{return!1}const i=r.origin===s;return e?ut(r.toString(),e)||i&&ut(r.pathname,e):i}else{const r=!!t.match(/^\/(?!\/)/);return e?ut(t,e):r}}function Al(t,e,n,r){const s=t.xhr,i=s==null?void 0:s[Ft];if(!s||s.__sentry_own_request__||!i)return;const{url:o,method:c}=i,a=ft()&&e(o);if(t.endTimestamp&&a){const _=s.__sentry_xhr_span_id__;if(!_)return;const A=r[_];A&&i.status_code!==void 0&&(Js(A,i.status_code),A.end(),delete r[_]);return}const u=ro(o),d=It(u||o),f=Pu(o),l=!!j(),p=a&&l?Kt({name:`${c} ${f}`,attributes:{url:o,type:"xhr","http.method":c,"http.url":u,"server.address":d==null?void 0:d.host,[L]:"auto.http.browser",[rt]:"http.client",...(d==null?void 0:d.search)&&{"http.query":d==null?void 0:d.search},...(d==null?void 0:d.hash)&&{"http.fragment":d==null?void 0:d.hash}}}):new pt;s.__sentry_xhr_span_id__=p.spanContext().spanId,r[s.__sentry_xhr_span_id__]=p,n(o)&&kl(s,ft()&&l?p:void 0);const h=I();return h&&h.emit("beforeOutgoingRequestSpan",p,t),p}function kl(t,e){const{"sentry-trace":n,baggage:r}=ki({span:e});n&&Nl(t,n,r)}function Nl(t,e,n){var s;const r=(s=t.__sentry_xhr_v3__)==null?void 0:s.request_headers;if(!(r!=null&&r["sentry-trace"]))try{if(t.setRequestHeader("sentry-trace",e),n){const i=r==null?void 0:r.baggage;(!i||!Cl(i))&&t.setRequestHeader("baggage",n)}}catch{}}function Cl(t){return t.split(",").some(e=>e.trim().startsWith("sentry-"))}function ro(t){try{return new URL(t,y.location.origin).href}catch{return}}function Pl(){y.document?y.document.addEventListener("visibilitychange",()=>{const t=j();if(!t)return;const e=D(t);if(y.document.hidden&&e){const n="cancelled",{op:r,status:s}=b(e);W&&m.log(`[Tracing] Transaction: ${n} -> since tab moved to the background, op: ${r}`),s||e.setStatus({code:O,message:n}),e.setAttribute("sentry.cancellation_reason","document.hidden"),e.end()}}):W&&m.warn("[Tracing] Could not set up background tab detection due to lack of global document")}const Ol=3600,so="sentry_previous_trace",Ll="sentry.previous_trace";function xl(t,{linkPreviousTrace:e,consistentTraceSampling:n}){const r=e==="session-storage";let s=r?Dl():void 0;t.on("spanStart",o=>{if(D(o)!==o)return;const c=v().getPropagationContext();s=$l(s,o,c),r&&Ml(s)});let i=!0;n&&t.on("beforeSampling",o=>{if(!s)return;const c=v(),a=c.getPropagationContext();if(i&&a.parentSpanId){i=!1;return}c.setPropagationContext({...a,dsc:{...a.dsc,sample_rate:String(s.sampleRate),sampled:String(Jn(s.spanContext))},sampleRand:s.sampleRand}),o.parentSampled=Jn(s.spanContext),o.parentSampleRate=s.sampleRate,o.spanAttributes={...o.spanAttributes,[Ys]:s.sampleRate}})}function $l(t,e,n){const r=b(e);function s(){var c,a;try{return Number((c=n.dsc)==null?void 0:c.sample_rate)??Number((a=r.data)==null?void 0:a[or])}catch{return 0}}const i={spanContext:e.spanContext(),startTimestamp:r.start_timestamp,sampleRate:s(),sampleRand:n.sampleRand};if(!t)return i;const o=t.spanContext;return o.traceId===r.trace_id?t:(Date.now()/1e3-t.startTimestamp<=Ol&&(W&&m.log(`Adding previous_trace ${o} link to span ${{op:r.op,...e.spanContext()}}`),e.addLink({context:o,attributes:{[oa]:"previous_trace"}}),e.setAttribute(Ll,`${o.traceId}-${o.spanId}-${Jn(o)?1:0}`)),i)}function Ml(t){try{y.sessionStorage.setItem(so,JSON.stringify(t))}catch(e){W&&m.warn("Could not store previous trace in sessionStorage",e)}}function Dl(){var t;try{const e=(t=y.sessionStorage)==null?void 0:t.getItem(so);return JSON.parse(e)}catch{return}}function Jn(t){return t.traceFlags===1}const Fl="BrowserTracing",Hl={...Be,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,enableElementTiming:!0,ignoreResourceSpans:[],ignorePerformanceApiSpans:[],detectRedirects:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...no},Bl=(t={})=>{const e={name:void 0,source:void 0},n=y.document,{enableInp:r,enableElementTiming:s,enableLongTask:i,enableLongAnimationFrame:o,_experiments:{enableInteractions:c,enableStandaloneClsSpans:a,enableStandaloneLcpSpans:u},beforeStartSpan:d,idleTimeout:f,finalTimeout:l,childSpanTimeout:p,markBackgroundSpan:h,traceFetch:_,traceXHR:A,trackFetchStreamPerformance:Y,shouldCreateSpanForRequest:st,enableHTTPTimings:Ot,ignoreResourceSpans:fn,ignorePerformanceApiSpans:Re,instrumentPageLoad:E,instrumentNavigation:M,detectRedirects:Lt,linkPreviousTrace:te,consistentTraceSampling:xt,onRequestSpanStart:St}={...Hl,...t};let N,it;function ot(w,C,R=!0){const P=C.op==="pageload",x=d?d(C):C,at=x.attributes||{};if(C.name!==x.name&&(at[J]="custom",x.attributes=at),!R){const ne=Nt();Kt({...x,startTime:ne}).end(ne);return}e.name=x.name,e.source=at[J];const ct=gi(x,{idleTimeout:f,finalTimeout:l,childSpanTimeout:p,disableAutoFinish:P,beforeSpanEnd:ne=>{N==null||N(),Yd(ne,{recordClsOnPageloadSpan:!a,recordLcpOnPageloadSpan:!u,ignoreResourceSpans:fn,ignorePerformanceApiSpans:Re}),ws(w,void 0);const vr=v(),oo=vr.getPropagationContext();vr.setPropagationContext({...oo,traceId:ct.spanContext().traceId,sampled:Pt(ct),dsc:lt(ne)})}});ws(w,ct);function ee(){n&&["interactive","complete"].includes(n.readyState)&&w.emit("idleSpanEnableAutoFinish",ct)}P&&n&&(n.addEventListener("readystatechange",()=>{ee()}),ee())}return{name:Fl,setup(w){if(Oa(),N=Ud({recordClsStandaloneSpans:a||!1,recordLcpStandaloneSpans:u||!1,client:w}),r&&yf(),s&&sf(),o&&T.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?qd():i&&jd(),c&&Gd(),Lt&&n){const R=()=>{it=$()};addEventListener("click",R,{capture:!0}),addEventListener("keydown",R,{capture:!0,passive:!0})}function C(){const R=le(w);R&&!b(R).timestamp&&(W&&m.log(`[Tracing] Finishing current active span with op: ${b(R).op}`),R.setAttribute(Ve,"cancelled"),R.end())}w.on("startNavigationSpan",(R,P)=>{if(I()!==w)return;if(P!=null&&P.isRedirect){W&&m.warn("[Tracing] Detected redirect, navigation span will not be the root span, but a child span."),ot(w,{op:"navigation.redirect",...R},!1);return}it=void 0,C(),Ct().setPropagationContext({traceId:dt(),sampleRand:Math.random()});const x=v();x.setPropagationContext({traceId:dt(),sampleRand:Math.random()}),x.setSDKProcessingMetadata({normalizedRequest:void 0}),ot(w,{op:"navigation",...R})}),w.on("startPageLoadSpan",(R,P={})=>{if(I()!==w)return;C();const x=P.sentryTrace||Rs("sentry-trace"),at=P.baggage||Rs("baggage"),ct=Ia(x,at),ee=v();ee.setPropagationContext(ct),ee.setSDKProcessingMetadata({normalizedRequest:pr()}),ot(w,{op:"pageload",...R})})},afterAllSetup(w){let C=he();if(te!=="off"&&xl(w,{linkPreviousTrace:te,consistentTraceSampling:xt}),y.location){if(E){const R=U();Ul(w,{name:y.location.pathname,startTime:R?R/1e3:void 0,attributes:{[J]:"url",[L]:"auto.pageload.browser"}})}M&&Tr(({to:R,from:P})=>{if(P===void 0&&(C==null?void 0:C.indexOf(R))!==-1){C=void 0;return}C=void 0;const x=Oi(R),at=le(w),ct=at&&Lt&&Gl(at,it);jl(w,{name:(x==null?void 0:x.pathname)||y.location.pathname,attributes:{[J]:"url",[L]:"auto.navigation.browser"}},{url:R,isRedirect:ct})})}h&&Pl(),c&&ql(w,f,l,p,e),r&&Tf(),Il(w,{traceFetch:_,traceXHR:A,trackFetchStreamPerformance:Y,tracePropagationTargets:w.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:st,enableHTTPTimings:Ot,onRequestSpanStart:St})}}};function Ul(t,e,n){t.emit("startPageLoadSpan",e,n),v().setTransactionName(e.name);const r=le(t);return r&&t.emit("afterStartPageLoadSpan",r),r}function jl(t,e,n){const{url:r,isRedirect:s}=n||{};t.emit("beforeStartNavigationSpan",e,{isRedirect:s}),t.emit("startNavigationSpan",e,{isRedirect:s});const i=v();return i.setTransactionName(e.name),r&&!s&&i.setSDKProcessingMetadata({normalizedRequest:{...pr(),url:r}}),le(t)}function Rs(t){const e=y.document,n=e==null?void 0:e.querySelector(`meta[name=${t}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}function ql(t,e,n,r,s){const i=y.document;let o;const c=()=>{const a="ui.action.click",u=le(t);if(u){const d=b(u).op;if(["navigation","pageload"].includes(d)){W&&m.warn(`[Tracing] Did not create ${a} span because a pageload or navigation span is in progress.`);return}}if(o&&(o.setAttribute(Ve,"interactionInterrupted"),o.end(),o=void 0),!s.name){W&&m.warn(`[Tracing] Did not create ${a} transaction because _latestRouteName is missing.`);return}o=gi({name:s.name,op:a,attributes:{[J]:s.source||"url"}},{idleTimeout:e,finalTimeout:n,childSpanTimeout:r})};i&&addEventListener("click",c,{capture:!0})}const io="_sentry_idleSpan";function le(t){return t[io]}function ws(t,e){B(t,io,e)}const As=1.5;function Gl(t,e){const n=b(t),r=Nt(),s=n.start_timestamp;return!(r-s>As||e&&r-e<=As)}function Wl(t){const e={...t};return Ai(e,"svelte"),Tl(e)}function ks(t){let e,n,r,s,i;return{c(){e=Et("div"),n=Et("h3"),n.textContent="Result:",r=$e(),s=Et("pre"),i=Zn(t[1]),re(s,"class","svelte-1etoc0j"),re(e,"class","result svelte-1etoc0j")},m(o,c){Ps(o,e,c),z(e,n),z(e,r),z(e,s),z(s,i)},p(o,c){c&2&&Os(i,o[1])},d(o){o&&Kn(e)}}}function zl(t){let e,n,r,s,i,o,c=t[0]?"Loading...":"Trigger Error",a,u,d,f,l=t[1]&&ks(t);return{c(){e=Et("main"),n=Et("h1"),n.textContent="Svelte Mini App",r=$e(),s=Et("p"),s.textContent=`Click the button to trigger an error in the Rails app and test distributed
    tracing:`,i=$e(),o=Et("button"),a=Zn(c),u=$e(),l&&l.c(),re(o,"id","trigger-error-btn"),o.disabled=t[0],re(o,"class","svelte-1etoc0j"),re(e,"class","svelte-1etoc0j")},m(p,h){Ps(p,e,h),z(e,n),z(e,r),z(e,s),z(e,i),z(e,o),z(o,a),z(e,u),l&&l.m(e,null),d||(f=lo(o,"click",t[2]),d=!0)},p(p,[h]){h&1&&c!==(c=p[0]?"Loading...":"Trigger Error")&&Os(a,c),h&1&&(o.disabled=p[0]),p[1]?l?l.p(p,h):(l=ks(p),l.c(),l.m(e,null)):l&&(l.d(1),l=null)},i:ae,o:ae,d(p){p&&Kn(e),l&&l.d(),d=!1,f()}}}function Vl(t,e,n){let r=!1,s="";async function i(){n(0,r=!0);const o=(void 0)({name:"trigger-error",op:"http.client"});v().setSpan(o);try{const c=await fetch("http://localhost:4000/error",{method:"GET",headers:{"Content-Type":"application/json"}});if(c.ok){const a=await c.json();n(1,s=`Success: ${JSON.stringify(a)}`)}else n(1,s=`Error: ${c.status} ${c.statusText}`)}catch(c){n(1,s=`Error: ${c.message}`)}finally{n(0,r=!1),o.finish()}}return[r,s,i]}class Yl extends Io{constructor(e){super(),vo(this,e,Vl,zl,uo,{})}}Wl({dsn:"http://12345@localhost/123",debug:!0,integrations:[Bl()],tracesSampleRate:1,tracePropagationTargets:["localhost",/^https?:\/\/localhost:4000/],environment:"test"});console.log("[Sentry] Initialized with debug=true and tracePropagationTargets for localhost:4000");new Yl({target:document.getElementById("app")});
